---
title: "Traefik Ingress Controller 完整部署指南"
date: 2021-01-17T16:15:32+08:00
draft: false
tags: [ "traefik", "ingress", "kubernetes", "网关"]
tags_weight: 80
categories: ["k8s","网络"]
categories_weight: 80
keywords:
- traefik
- ingress controller
- 负载均衡
- 反向代理
- kubernetes
- 服务网关
- 自动发现
description: "从零开始学习 Traefik：详细介绍如何在 Kubernetes 中部署和配置 Traefik Ingress Controller，实现智能路由和负载均衡"
---

# Traefik 简介

## 什么是 Traefik？

Traefik 是一个现代化的云原生反向代理和负载均衡器，专为微服务架构设计。它就像一个智能的"交通指挥员"，能够自动发现您的服务并为它们配置路由规则。

### 为什么选择 Traefik？

与传统的 Nginx 或 Apache 相比，Traefik 具有以下显著优势：

| 特性 | 传统代理 | Traefik |
|------|----------|---------|
| 配置方式 | 手动编辑配置文件 | 自动服务发现 |
| 配置更新 | 需要重启服务 | 动态实时更新 |
| 容器支持 | 需要额外配置 | 原生支持 |
| 监控面板 | 需要第三方工具 | 内置 Web UI |

### 核心优势

- **🔍 自动服务发现**：无需手动配置，自动检测新服务
- **⚡ 动态配置**：实时更新路由规则，零停机时间
- **☁️ 云原生设计**：完美支持 Docker、Kubernetes 等平台
- **🛠️ 丰富中间件**：内置认证、限流、重试等功能
- **📊 可观测性**：提供详细的监控指标和链路追踪

### 应用场景

Traefik 特别适合以下场景：
- **微服务架构**：自动管理多个微服务的路由
- **容器化部署**：与 Docker/Kubernetes 无缝集成
- **API 网关**：统一管理 API 访问入口
- **负载均衡**：智能分发请求到多个后端实例

### 架构概览

![Traefik 架构图](https://cdn.treesir.pub/img/traefik-architecture.png)

*图：Traefik 的整体架构，展示了请求从客户端到后端服务的完整流程*

# 核心概念详解

理解 Traefik 的核心概念是正确使用它的关键。我们可以把 Traefik 想象成一个智能的"快递分拣中心"：

## 1. Providers（提供者）- 信息来源

**作用**：告诉 Traefik 从哪里获取服务信息

| 提供者类型 | 说明 | 使用场景 |
|-----------|------|----------|
| **Kubernetes** | 从 K8s API 获取服务信息 | 容器化部署 |
| **Docker** | 监控 Docker 容器标签 | 单机 Docker 环境 |
| **File** | 从配置文件读取规则 | 静态配置 |
| **Consul/Etcd** | 从键值存储获取配置 | 分布式配置管理 |

## 2. EntryPoints（入口点）- 门户

**作用**：定义 Traefik 监听的网络端口，就像建筑物的不同入口

```yaml
# 常见入口点配置
entryPoints:
  web:        # HTTP 入口（端口 80）
  websecure:  # HTTPS 入口（端口 443）
  traefik:    # 管理界面（端口 8080）
```

## 3. Routers（路由器）- 智能分拣员

**作用**：分析传入请求并决定路由到哪个服务

### 路由匹配规则

| 匹配类型 | 示例 | 说明 |
|---------|------|------|
| **Host** | `api.example.com` | 基于域名路由 |
| **Path** | `/api/v1` | 基于路径路由 |
| **Headers** | `X-API-Version: v2` | 基于请求头路由 |
| **Method** | `GET`, `POST` | 基于 HTTP 方法路由 |

### 路由规则示例

```yaml
# 复合路由规则
match: Host(`api.example.com`) && PathPrefix(`/v1`) && Method(`GET`)
```

## 4. Services（服务）- 目标地址

**作用**：定义后端服务的负载均衡策略

### 主要功能

- **🔄 负载均衡**：支持轮询、加权轮询、最少连接等算法
- **💓 健康检查**：自动检测后端服务健康状态
- **🔀 故障转移**：自动切换到健康的后端实例

## 5. Middlewares（中间件）- 处理加工站

**作用**：在请求到达后端服务前进行预处理

### 常用中间件类型

| 中间件类型 | 功能 | 使用场景 |
|-----------|------|----------|
| **认证** | BasicAuth、OAuth、JWT | 用户身份验证 |
| **限流** | 基于 IP 或用户的请求限制 | 防止 API 滥用 |
| **重写** | URL 重写和重定向 | 路径转换 |
| **压缩** | 响应内容压缩 | 提升传输效率 |
| **CORS** | 跨域资源共享配置 | 前端跨域访问 |

## 请求处理流程

```mermaid
graph LR
    A[客户端请求] --> B[EntryPoint<br/>端口监听]
    B --> C[Router<br/>路由匹配]
    C --> D[Middleware<br/>请求处理]
    D --> E[Service<br/>负载均衡]
    E --> F[后端Pod<br/>应用服务]
```

**流程说明：**
1. **客户端发起请求**：用户访问应用
2. **EntryPoint 接收**：Traefik 在指定端口监听请求
3. **Router 路由匹配**：根据域名、路径等规则匹配路由
4. **Middleware 处理**：执行认证、限流等中间件逻辑
5. **Service 负载均衡**：选择健康的后端实例
6. **转发到后端**：将请求发送到目标应用

# 环境准备

## 系统要求

在开始部署之前，请确保您的环境满足以下要求：

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| Kubernetes | v1.19+ | 支持 Ingress API v1 |
| Helm | v3.4.2+ | 用于部署 Traefik Chart |
| 操作系统 | CentOS 7.9+ | 或其他 Linux 发行版 |
| 网络 | LoadBalancer 支持 | 云环境或 MetalLB |

## 实验环境信息

本教程基于以下环境进行演示：

### 软件版本
- **Traefik**: v2.3.6
- **Helm**: v3.4.2
- **Kubernetes**: v1.19.6
- **操作系统**: CentOS Linux 7.9.2009

### 集群节点信息
| 节点角色 | IP 地址 | 说明 |
|---------|---------|------|
| Master01 | ************ | 控制平面节点 |
| Node01 | ************ | 工作节点 |
| Node02 | ************ | 工作节点 |
| Node03 | ************ | 工作节点 |

## 工具准备

### 安装 Helm（如果未安装）

```bash
# 下载 Helm 安装脚本
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# 验证安装
helm version
```

### 配置 Helm 自动补全

为了提高操作效率，建议配置命令自动补全：

```bash
# Bash 用户
helm completion bash >> ~/.bashrc
source ~/.bashrc

# Zsh 用户
helm completion zsh >> ~/.zshrc
source ~/.zshrc

# Fish 用户
helm completion fish > ~/.config/fish/completions/helm.fish
```

### 验证 Kubernetes 集群

```bash
# 检查集群状态
kubectl cluster-info

# 检查节点状态
kubectl get nodes

# 检查 Helm 是否能正常工作
helm list
```

# Traefik 部署

## 步骤 1：获取 Helm Chart

我们使用官方的 Helm Chart 来部署 Traefik：

```bash
# 克隆官方 Helm Chart 仓库
git clone https://github.com/traefik/traefik-helm-chart

# 进入 Chart 目录
cd traefik-helm-chart

# 查看默认配置（可选）
helm show values ./traefik/
```

## 步骤 2：创建自定义配置

根据我们的需求创建自定义配置文件：

```bash
cat > prod-values.yaml << 'EOF'
# 禁用默认的 Dashboard IngressRoute，我们将手动创建
ingressRoute:
  dashboard:
    enabled: false

# 配置端口映射
ports:
  web:
    port: 8000
    hostPort: 80      # 直接绑定到主机的 80 端口
  websecure:
    port: 8443
    hostPort: 443     # 直接绑定到主机的 443 端口

# 禁用 Service（使用 hostPort 模式时不需要）
service:
  enabled: false

# 配置日志级别
logs:
  general:
    level: ERROR      # 生产环境建议使用 ERROR 级别

# 容忍主节点污点，允许在 master 节点运行
tolerations:
- key: "node-role.kubernetes.io/master"
  operator: "Equal"
  effect: "NoSchedule"

# 固定部署到 master01 节点
nodeSelector:
  kubernetes.io/hostname: "master01"
EOF
```

### 配置说明

| 配置项 | 说明 | 原因 |
|-------|------|------|
| `hostPort` | 直接绑定主机端口 | 简化网络配置，适合单节点部署 |
| `service.enabled: false` | 禁用 Service | hostPort 模式下不需要 Service |
| `logs.level: ERROR` | 设置日志级别 | 减少日志输出，提升性能 |
| `tolerations` | 容忍主节点污点 | 允许在 master 节点运行 |
| `nodeSelector` | 节点选择器 | 固定部署位置，便于管理 |

## 步骤 3：部署 Traefik

```bash
# 创建命名空间
kubectl create namespace traefik

# 使用 Helm 部署 Traefik
helm upgrade --install traefik \
  --namespace traefik \
  --values ./prod-values.yaml \
  ./traefik/

# 等待 Pod 启动完成
watch kubectl get pods -n traefik
```

**预期输出：**
```bash
NAME                      READY   STATUS    RESTARTS   AGE
traefik-84879ffbd-6kwv5   1/1     Running   0          68s
```

## 步骤 4：验证部署

### 检查 Pod 状态

```bash
# 查看 Pod 详细信息
kubectl get pods -n traefik -o wide

# 查看 Pod 日志
kubectl logs -n traefik deployment/traefik
```

### 测试连通性

```bash
# 测试 HTTP 端口（应该返回 404，这是正常的）
curl -I http://************

# 预期输出
HTTP/1.1 404 Not Found
Date: Sun, 17 Jan 2021 03:09:37 GMT
Content-Length: 19
Content-Type: text/plain; charset=utf-8
```

> ✅ **成功标志**：看到 `404 page not found` 表示 Traefik 已正常启动，只是还没有配置任何路由规则

![image-20210117110937322](https://cdn.treesir.pub/img/image-20210117110937322.png)

# 配置 Traefik Dashboard

## 为什么需要配置 Dashboard？

Traefik Dashboard 是一个 Web 管理界面，提供以下功能：
- **实时监控**：查看当前路由规则和服务状态
- **流量统计**：监控请求数量和响应时间
- **配置查看**：可视化查看所有配置信息
- **故障排查**：快速定位路由问题

## 安全考虑

默认的 Dashboard 没有任何认证保护，任何人都可以访问。为了安全，我们需要：
1. 添加 BasicAuth 认证
2. 限制访问域名
3. 可选：配置 HTTPS

## 步骤 1：生成认证信息

### 安装密码生成工具

```bash
# CentOS/RHEL
yum install httpd-tools -y

# Ubuntu/Debian
apt-get install apache2-utils -y
```

### 生成用户密码

```bash
# 生成用户名和密码（用户名：admin，密码：123456）
htpasswd -nb admin 123456

# 输出示例
admin:$apr1$aUlPDYOb$tDDau3d3zv0op6NrMo6C1

# 转换为 base64 编码（Kubernetes Secret 需要）
htpasswd -nb admin 123456 | base64
# 输出：YWRtaW46JGFwcjEkYVVsUERZT2IkdEREYXUzZDN6dnowb3A2TnJNbzZDMQoK
```

> 🔐 **安全提示**：在生产环境中，请使用更强的密码



## 步骤 2：创建 Dashboard 配置

创建包含认证、中间件和路由的完整配置：

```yaml
cat > dashboard-config.yaml << 'EOF'
# 创建存储认证信息的 Secret
apiVersion: v1
kind: Secret
metadata:
  name: dashboard-auth-secret
  namespace: traefik
type: Opaque
data:
  users: YWRtaW46JGFwcjEkYVVsUERZT2IkdEREYXUzZDN6dnowb3A2TnJNbzZDMQoK

---
# 创建 BasicAuth 中间件
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: dashboard-auth
  namespace: traefik
spec:
  basicAuth:
    secret: dashboard-auth-secret
    removeHeader: true    # 移除认证头，避免传递给后端

---
# 创建 Dashboard 路由
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: traefik-dashboard
  namespace: traefik
spec:
  entryPoints:
  - web                   # 使用 HTTP 入口点
  routes:
  - match: Host(`traefik.coderyzun.cyou`)
    kind: Rule
    services:
    - name: api@internal  # Traefik 内置的 API 服务
      kind: TraefikService
    middlewares:
    - name: dashboard-auth
EOF
```

### 配置说明

| 组件 | 作用 | 说明 |
|------|------|------|
| **Secret** | 存储认证信息 | 包含用户名和密码的 base64 编码 |
| **Middleware** | 认证中间件 | 拦截请求进行身份验证 |
| **IngressRoute** | 路由规则 | 定义如何访问 Dashboard |

## 步骤 3：部署配置

```bash
# 应用配置
kubectl apply -f dashboard-config.yaml

# 验证资源创建
kubectl get secret,middleware,ingressroute -n traefik
```



## 设置域名解析

![image-20210117114010165](https://cdn.treesir.pub/img/image-20210117114010165.png)





![image-20210117114623277](https://cdn.treesir.pub/img/image-20210117114623277.png)

### Dashboard 功能介绍

- **HTTP Routers**：查看所有 HTTP 路由规则
- **HTTP Services**：查看后端服务状态
- **HTTP Middlewares**：查看中间件配置
- **Entrypoints**：查看入口点配置

# 中间件实战演示

中间件是 Traefik 的强大功能之一，可以在请求到达后端服务之前对其进行处理。本节将通过实际示例演示几种常用中间件的配置和使用。

> 📚 **参考文档**：[Traefik 官方中间件文档](https://doc.traefik.io/traefik/)

## 准备测试应用

在演示中间件功能之前，我们需要部署一个测试应用。我们将使用 `whoami` 应用，它会返回请求的详细信息。

### 部署测试应用

```bash
cat <<EOF | kubectl apply -f -
# 创建测试应用的 Service
apiVersion: v1
kind: Service
metadata:
  name: whoami-v1
  namespace: default
spec:
  ports:
    - protocol: TCP
      name: web
      port: 80
  selector:
    app: whoami-v1

---
# 创建测试应用的 Deployment
kind: Deployment
apiVersion: apps/v1
metadata:
  name: whoami-v1
  namespace: default
  labels:
    app: whoami-v1
spec:
  replicas: 2
  selector:
    matchLabels:
      app: whoami-v1
  template:
    metadata:
      labels:
        app: whoami-v1
    spec:
      containers:
        - name: whoami
          image: containous/whoami
          ports:
            - name: web
              containerPort: 80
EOF
```

### 验证应用部署

```bash
# 检查 Pod 状态
kubectl get pods -l app=whoami-v1

# 检查 Service
kubectl get svc whoami-v1
```

## 中间件示例

### 1. Headers 中间件 - 请求头处理

Headers 中间件可以添加、修改或删除 HTTP 请求头和响应头。

![headers](https://cdn.treesir.pub/img/headers.png)

#### 应用场景
- 添加安全头信息
- 传递用户身份信息
- 移除敏感头信息
- 设置 CORS 头


#### 创建 Headers 中间件

```yaml
cat <<EOF | kubectl apply -f -
# 添加请求头和响应头的中间件
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: add-headers
  namespace: default
spec:
  headers:
    customRequestHeaders:
      X-Script-Name: "traefik-demo"      # 添加自定义请求头
      X-Forwarded-Proto: "http"         # 添加协议信息
    customResponseHeaders:
      X-Custom-Response-Header: "Traefik-Powered"  # 添加自定义响应头
      X-Frame-Options: "DENY"           # 安全头：防止页面被嵌入框架

---
# 移除请求头的中间件
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: remove-headers
  namespace: default
spec:
  headers:
    customRequestHeaders:
      X-Script-Name: ""                 # 移除请求头（设置为空字符串）
    customResponseHeaders:
      X-Custom-Response-Header: ""      # 移除响应头
EOF
```

#### 创建路由规则

```yaml
cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: headers-demo
  namespace: default
spec:
  entryPoints:
    - web
  routes:
  # 添加头信息的路由
  - match: Host(`traefik.coderyzun.cyou`) && PathPrefix(`/headers/add`)
    kind: Rule
    services:
    - name: whoami-v1
      port: 80
    middlewares:
    - name: add-headers

  # 移除头信息的路由
  - match: Host(`traefik.coderyzun.cyou`) && PathPrefix(`/headers/remove`)
    kind: Rule
    services:
    - name: whoami-v1
      port: 80
    middlewares:
    - name: remove-headers
EOF
```

#### 测试 Headers 中间件

```bash
# 测试添加头信息
curl -H "X-Script-Name: original" http://traefik.coderyzun.cyou/headers/add

# 测试移除头信息
curl -H "X-Script-Name: will-be-removed" http://traefik.coderyzun.cyou/headers/remove
```

![image-20210117172109153](https://cdn.treesir.pub/img/image-20210117172109153.png)

### IPWhiteList

![ipwhitelist](https://cdn.treesir.pub/img/ipwhitelist.png)

> 只允许 `*************`，可访问
>
> ![image-20210117171701017](https://cdn.treesir.pub/img/image-20210117171701017.png)

创建 Middleware 资源清单

```bash
cat << EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: ipwhitelist
spec:
  ipWhiteList:
    sourceRange:
      - *************
EOF
```

创建 routes 清单

```bash
cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: apps-ipwhitelist
spec:
  entryPoints:
    - web
  routes:
  - match: Host(\`traefik.coderyzun.cyou\`) && PathPrefix(\`/ipwhitelist\`)
    kind: Rule
    services:
    - name: v1
      port: 80
    middlewares:
    - name: ipwhitelist 
EOF
```

![image-20210117175431908](https://cdn.treesir.pub/img/image-20210117175431908.png)

其他主机 执行 curl

![image-20210117175457262](https://cdn.treesir.pub/img/image-20210117175457262.png)

### RedirectRegex

创建 `v2` , deployment 和 service 资源清单

```bash
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: v2
spec:
  ports:
    - protocol: TCP
      name: web
      port: 80
  selector:
    app: v2

---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: v2
  labels:
    app: v2
spec:
  selector:
    matchLabels:
      app: v2
  template:
    metadata:
      labels:
        app: v2
    spec:
      containers:
        - name: v2
          image: containous/whoami
          ports:
            - name: web
              containerPort: 80
EOF
```

![image-20210117180923331](https://cdn.treesir.pub/img/image-20210117180923331.png)

创建 Middleware 资源清单

```bash
cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: Middleware
metadata:
  name: redirectregex
spec:
  redirectRegex:
    regex: ^http://traefik.coderyzun.cyou/redirectregex/v1/(.*)
    replacement: http://traefik.coderyzun.cyou/redirectregex/v2/\${1}
    permanent: true   # open permanent redirection 
EOF
```

创建 routes 清单

```bash
cat <<EOF | kubectl apply -f -
apiVersion: traefik.containo.us/v1alpha1
kind: IngressRoute
metadata:
  name: redirectregex-appv1
  namespace: default
spec:
  entryPoints:
    - web
  routes:
  - match: Host(\`traefik.coderyzun.cyou\`) && PathPrefix(\`/redirectregex/v1\`)
    kind: Rule
    services:
    - name: v1
      port: 80
    middlewares:
    - name: redirectregex
  - match: Host(\`traefik.coderyzun.cyou\`) && PathPrefix(\`/redirectregex/v2\`)
    kind: Rule
    services:
    - name: v2
      port: 80
EOF
```

![image-20210117180938144](https://cdn.treesir.pub/img/image-20210117180938144.png)



测试 

```bash
curl -I http://traefik.coderyzun.cyou/redirectregex/v1/1
```

![image-20210117181500974](https://cdn.treesir.pub/img/image-20210117181500974.png)

