<svg width="1920" height="1080" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- GitHub 风格渐变 -->
    <linearGradient id="githubGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f6f8fa;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f1f3f4;stop-opacity:1" />
    </linearGradient>
    
    <!-- 微妙的网格图案 -->
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#e1e4e8" stroke-width="1" opacity="0.3"/>
    </pattern>
    
    <!-- 光晕效果 -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
    
    <!-- 阴影效果 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-color="#000000" flood-opacity="0.1"/>
    </filter>
  </defs>
  
  <!-- 背景 -->
  <rect width="1920" height="1080" fill="url(#githubGrad)"/>
  <rect width="1920" height="1080" fill="url(#grid)"/>
  
  <!-- GitHub 风格几何元素 -->
  <!-- 代码块图标 -->
  <g transform="translate(200, 150)" filter="url(#shadow)">
    <rect x="0" y="0" width="60" height="40" rx="6" fill="#0969da" opacity="0.1"/>
    <rect x="8" y="8" width="44" height="4" rx="2" fill="#0969da" opacity="0.3"/>
    <rect x="8" y="16" width="32" height="4" rx="2" fill="#0969da" opacity="0.3"/>
    <rect x="8" y="24" width="36" height="4" rx="2" fill="#0969da" opacity="0.3"/>
  </g>
  
  <!-- 提交图标 -->
  <g transform="translate(1600, 200)" filter="url(#shadow)">
    <circle cx="20" cy="20" r="20" fill="#1a7f37" opacity="0.1"/>
    <circle cx="20" cy="20" r="8" fill="#1a7f37" opacity="0.4"/>
    <circle cx="20" cy="20" r="3" fill="#1a7f37"/>
  </g>
  
  <!-- 分支图标 -->
  <g transform="translate(300, 800)" filter="url(#shadow)">
    <path d="M10,10 Q20,20 30,10 Q40,20 50,10" stroke="#8250df" stroke-width="3" fill="none" opacity="0.4"/>
    <circle cx="10" cy="10" r="4" fill="#8250df" opacity="0.6"/>
    <circle cx="50" cy="10" r="4" fill="#8250df" opacity="0.6"/>
  </g>
  
  <!-- 星星图标 -->
  <g transform="translate(1500, 800)" filter="url(#shadow)">
    <polygon points="20,5 25,15 35,15 27,23 30,33 20,28 10,33 13,23 5,15 15,15" 
             fill="#ffd700" opacity="0.3"/>
  </g>
  
  <!-- 浮动的代码符号 -->
  <text x="400" y="300" font-family="monospace" font-size="24" fill="#0969da" opacity="0.2" filter="url(#glow)">
    &lt;/&gt;
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 0,-10; 0,0" dur="4s" repeatCount="indefinite"/>
  </text>
  
  <text x="1200" y="400" font-family="monospace" font-size="20" fill="#1a7f37" opacity="0.2" filter="url(#glow)">
    { }
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 0,8; 0,0" dur="3.5s" repeatCount="indefinite"/>
  </text>
  
  <text x="800" y="700" font-family="monospace" font-size="18" fill="#8250df" opacity="0.2" filter="url(#glow)">
    [ ]
    <animateTransform attributeName="transform" type="translate" 
                      values="0,0; 0,-6; 0,0" dur="4.2s" repeatCount="indefinite"/>
  </text>
  
  <!-- 微妙的连接线 -->
  <g opacity="0.1">
    <path d="M200,200 Q600,300 1000,250" stroke="#0969da" stroke-width="2" fill="none">
      <animate attributeName="stroke-dasharray" values="0,1000; 1000,0; 0,1000" dur="8s" repeatCount="indefinite"/>
    </path>
    <path d="M1600,250 Q1200,400 800,350" stroke="#1a7f37" stroke-width="2" fill="none">
      <animate attributeName="stroke-dasharray" values="0,800; 800,0; 0,800" dur="6s" repeatCount="indefinite"/>
    </path>
  </g>
  
  <!-- 底部波浪 -->
  <path d="M0,900 Q480,880 960,900 T1920,900 L1920,1080 L0,1080 Z" 
        fill="#ffffff" opacity="0.3"/>
  <path d="M0,940 Q480,920 960,940 T1920,940 L1920,1080 L0,1080 Z" 
        fill="#f6f8fa" opacity="0.5"/>
</svg>
