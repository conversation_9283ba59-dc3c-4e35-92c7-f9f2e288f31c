---
title: "Sonatype Nexus Repository（Nexus3） 私服文件下载至本地 - (使用进阶篇 一)"
date: 2023-07-25T20:47:22+08:00
draft: false
tags: [ "Nexus3","DevOps"]
tags_weight: 80
categories: ["SRE"]
categories_weight: 80
keywords:
- Nexus3
- Nexus
- 下载
- Sonatype
- 下载
- 至本地
description: "Sonatype Nexus Repository (Nexus3) Private Server File Download to Local - Advanced Usage. Sonatype Nexus Repository（Nexus3） 私服文件下载至本地 - ( 使用进阶篇 )"
---

# 说明

## Sonatype Nexus Repository 是什么？

> - Sonatype Nexus Repository 是一个用于管理和分发软件组件的开源仓库管理系统。它提供了一个集中化的平台，使开发人员能够有效地存储、共享和发布各种类型的软件包。Nexus Repository 支持多种主流技术栈，并具有强大的安全性和可扩展性。通过使用 Sonatype Nexus Repository，团队可以更好地控制其软件构建过程，并确保高质量和稳定性的交付。` by ChatCPT`



## Nexus3 私服文件下载至本地的用途是什么？

> 1. 比如我们要将 `Nexus3` 私服中的依赖包制品，`进行迁移`，因 Nexus3 基于 [BlobStore](https://help.sonatype.com/repomanager3/planning-your-implementation/storage-guide) 技术实现对文件的落盘存储，无法在对应节点中直接看到制品文件，此时则需要通过一些手段进行转换取出，虽然可以通过网页一个一个点击下载，但如果下载制品数量过多，则不会是个过于明智的选择。迁移的场景也有很多种，如:
>    - 迁移至其他实例，或离线环境
>    - 需要更换到其他类型的存储库如:  `JFrog`

# 文件下载至本地

> 下面所展示的方法，使用到了 Nexus3 的API 进行实现，具体可参考如下文档：
>
> - https://help.sonatype.com/repomanager3/integrations/rest-and-integration-api
>
> 下述脚本，对特殊类私服未做测试如 `Docker`, 目前已测试支持,`且通过`的私服类型有: `Maven`、`NPM`、`PYPI`、`RAW`。

## 脚本如下

> 使用说明，更改如下变量为你实际的
>
> - NEXUS_USER:  实例用户名
> - NEXUS_PASS: 实例密码
> - NEXUS_URL: 实例URL
> - NEXUS_REPO: 需要下载至本地的私服地址
>
> 使用前还需确保已经安装了 [jq](https://www.linode.com/docs/guides/using-jq-to-process-json-on-the-command-line/)
```bash
#!/bin/bash

NEXUS_USER='admin'
NEXUS_PASS='xxxx'
NEXUS_URL="https://$NEXUS_USER:$<EMAIL>"
NEXUS_REPO='static-file'

# ---
cToken=''
count=0
DOWNLOAD_PATH=${NEXUS_REPO}
NEXUS_HTTP_URL=$(echo $NEXUS_URL|sed "s|$NEXUS_USER:$NEXUS_PASS@||g")

function assetsPage(){
    if [ "${count}" -eq 0 ];then
        JSON=`curl -X 'GET'  "${NEXUS_URL}/service/rest/v1/assets?repository=${NEXUS_REPO}"  -H 'accept: application/json' 2>/dev/null`
    else
        JSON=`curl -X 'GET'  "${NEXUS_URL}/service/rest/v1/assets?repository=${NEXUS_REPO}&continuationToken=${cToken}"  -H 'accept: application/json' 2>/dev/null`
        # cToken=''
    fi

    if [ -n "${JSON}" ];then
    	cToken=`echo "${JSON}"| jq .continuationToken |sed "s|\"||g"`
    else 
	exit 1
    fi

    if [ -n ${cToken} ];then
        echo "${JSON}"| jq .items[].downloadUrl | sed "s|\"||g"
        let count++
        assetsPage
    elif [ "${cToken}" == 'null' ];then
	echo "${JSON}"| jq .items[].downloadUrl | sed "s|\"||g"
    fi
}

function downAssetsUploadAssets(){
    for URL in $(assetsPage);do
        UPLOAD_URL=$(echo "$URL"|sed "s|${NEXUS_HTTP_URL}/repository/${NEXUS_REPO}/||g")
        if [ ! $(echo "${UPLOAD_URL}"|grep '/'|wc -l) -eq 0 ];then
            mkdir -p  ./${DOWNLOAD_PATH}/"${UPLOAD_URL%/*}"
            wget --no-check-certificate -O "./${DOWNLOAD_PATH}/${UPLOAD_URL}" "${URL}" >> download.log 2>&1
        else
            mkdir -p "${SOURCE_REPO}"
            wget --no-check-certificate -O "./${DOWNLOAD_PATH}/${UPLOAD_URL}" "${URL}" >> download.log 2>&1
        fi
    done
}


# assetsPage
downAssetsUploadAssets

```

## 使用效果展示

![image-20230725213915222](https://cdn.treesir.pub/img/image-20230725213915222.png)

![image-20230725214858445](https://cdn.treesir.pub/img/image-20230725214858445.png)

# 总结

> 通过上面这个脚本就可以轻松一键的将文件转储下载至本地了，后面我再更新一下如何将 `Maven`、`NPM`、`PYPI`、`RAW` 这类私服制品文件，`迁移` 上传至其他`实例`或平台中，比如: `JForg`。
