---
title: "Velero 备份迁移工具安装与配置"
date: 2021-05-16T18:00:36+08:00
draft: false
tags: [ "Velero", "docker-compose", "backup"]
tags_weight: 80
categories: ["devops","k8s"]
categories_weight: 80
keywords:
- Velero
- docker-compose
- backup
- devops
- k8s
- kubernetes
description: "Velero 备份迁移工具的安装配置及基本使用方法"
---

# 环境准备

## 安装基础依赖

安装 Docker Compose：

```bash
yum install -y docker-compose
```

  

## 部署 MinIO 对象存储

创建工作目录并配置 MinIO：

```bash
mkdir -p /data/docker-compose/minio && cd /data/docker-compose/minio

cat > docker-compose.yaml << EOF
version: "2.0"
services:
  minio:
    image: minio/minio:RELEASE.2021-03-17T02-33-02Z  # 使用稳定版本镜像
    container_name: minio
    hostname: minio
    restart: always
    tty: true
    ports:
      - "9000:9000"
    volumes:
      - /application/minio/data:/data
      - /application/minio/config:/root/.minio
    environment:
      - "MINIO_ACCESS_KEY=admin"
      - "MINIO_SECRET_KEY=12345678"
    command: "server /data"
EOF

docker-compose up -d  # 启动容器

docker-compose logs -f  # 查看容器启动日志
```

> 更多环境变量配置请参考 [MinIO 官方文档](https://docs.min.io/docs/minio-server-configuration-guide.html)

# 安装 Velero

## 下载并安装 Velero 客户端

```bash
mkdir -p /data/velero && cd /data/velero

# 创建 AWS 凭证文件
cat > credentials-velero << EOF
[default]
aws_access_key_id = admin
aws_secret_access_key = 12345678
EOF

# 下载 Velero 二进制文件
wget https://github.com/vmware-tanzu/velero/releases/download/v1.6.0/velero-v1.6.0-linux-amd64.tar.gz

# 解压并安装
tar xf velero-v1.6.0-linux-amd64.tar.gz
cp -a velero-v1.6.0-linux-amd64/velero /usr/local/bin/

# 验证安装
velero version
```

**预期输出**：
```
Client:
        Version: v1.6.0
        Git commit: 5bd70fd8eef316d220317245e46dc6016c348dce
<error getting server version: no matches for kind "ServerStatusRequest" in version "velero.io/v1">
```

> **注意**：此时显示服务端错误是正常的，因为还未在集群中安装 Velero 服务端组件。

## 在 Kubernetes 集群中部署 Velero

### 创建命名空间

```bash
kubectl create ns velero
```

### 安装 Velero 服务端组件

```bash
velero install \
    --image velero/velero:v1.2.0 \
    --provider aws \
    --bucket velero \
    --namespace velero \
    --secret-file ./credentials-velero \
    --velero-pod-cpu-request 200m \
    --velero-pod-mem-request 200Mi \
    --velero-pod-cpu-limit 1000m \
    --velero-pod-mem-limit 1000Mi \
    --use-volume-snapshots=false \
    --use-restic \
    --restic-pod-cpu-request 200m \
    --restic-pod-mem-request 200Mi \
    --restic-pod-cpu-limit 1000m \
    --restic-pod-mem-limit 1000Mi \
    --plugins velero/velero-plugin-for-aws:v1.2.0 \
    --backup-location-config region=minio,s3ForcePathStyle="true",s3Url=http://nps.treesir.pub:1181
```

**参数说明**：
- `--image`：指定 Velero 镜像版本
- `--provider`：存储提供商（使用 AWS S3 兼容接口）
- `--bucket`：存储桶名称
- `--secret-file`：AWS 凭证文件路径
- `--use-volume-snapshots=false`：禁用卷快照功能
- `--use-restic`：启用 Restic 进行文件系统备份
- `--backup-location-config`：配置备份存储位置

# 基本使用

## 创建备份

创建集群备份，排除系统命名空间：

```bash
velero backup create all-tidy --exclude-namespaces kube-system,ingress-nginx,cattle-system,velero
```

## 查看备份状态

```bash
velero backup get
velero backup describe all-tidy
```

> **参考资料**：[使用 Velero 备份 Kubernetes 集群](https://www.chenshaowen.com/blog/backup-kubernetes-cluster-using-velero.html)

