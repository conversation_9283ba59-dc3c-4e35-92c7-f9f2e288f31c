version: '3.8'

services:
  blog:
    image: proxyhub.cpinnov.run/yangzun/blog:latest
    container_name: yangzun-blog
    restart: always
    ports:
      - "80:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      # 可选：挂载自定义 nginx 配置
      # - ./nginx.conf:/etc/nginx/nginx.conf:ro
      # 可选：挂载日志目录
      - ./logs:/var/log/nginx
    networks:
      - blog-network
    labels:
      - "com.docker.compose.project=yangzun-blog"
      - "description=Hugo Blog powered by Nginx"
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

networks:
  blog-network:
    driver: bridge
    name: yangzun-blog-network

volumes:
  logs:
    driver: local