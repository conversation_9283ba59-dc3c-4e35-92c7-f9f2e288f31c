---
title: "Kind 部署本地k8s集群的使用记录"
date: 2020-11-15T14:24:42+08:00
draft: false
tags: [ "centos7","kind"]
tags_weight: 22
categories: ["k8s","docker","devops"]
categories_weight: 22
---

## 参考资料
- [Github 地址](https://github.com/kubernetes-sigs/kind)
- [参考博客](https://blog.tianfeiyu.com/2019/09/06/kind_deploy/)

## 安装

```bash
curl -Lo ./kind "https://kind.sigs.k8s.io/dl/v0.9.0/kind-$(uname)-amd64"
chmod +x ./kind
mv ./kind /some-dir-in-your-PATH/kind
```

## 启动集群

> 注意启动集群前 请确认 docker 服务是否启动

```bash
docker info|grep -A 2 Server  # 确认 是否启动
Server:
 Containers: 1
  Running: 1
--
 Server Version: 19.03.13
 Storage Driver: overlay2
  Backing Filesystem: extfs
  
  
  
kind create cluster  # 启动  
```

## 配置 kubectl

```bash
mkdir -p ~/.kube
kind get kubeconfig >> ~/.kube/kind-config-kind

kubectl cluster-info --context kind-kind  # 切换集群

~  kubectl get pod --all-namespaces
NAMESPACE            NAME                                         READY   STATUS    RESTARTS   AGE
kube-system          coredns-f9fd979d6-w6mhs                      1/1     Running   0          14m
kube-system          coredns-f9fd979d6-xrlp8                      1/1     Running   0          14m
kube-system          etcd-kind-control-plane                      1/1     Running   0          14m
kube-system          kindnet-l66x7                                1/1     Running   0          14m
kube-system          kube-apiserver-kind-control-plane            1/1     Running   0          14m
kube-system          kube-controller-manager-kind-control-plane   1/1     Running   0          14m
kube-system          kube-proxy-r6qk9                             1/1     Running   0          14m
kube-system          kube-scheduler-kind-control-plane            1/1     Running   0          14m
local-path-storage   local-path-provisioner-78776bfc44-hfpvq      1/1     Running   0          14m
```

## 添加别名方便后期使用

```bash
echo "alias local-k8s=\"kubectl cluster-info --context kind-kind\"" >> ~/.zshrc   # 配置完成后重启一下终端 (linux 系统为 "~/.bashrc")
```

## 集群管理

### 删除集群

```bash
kind delete cluster
```

### 部署原生 k8s dashboard

```bash
kubectl apply -f https://raw.githubusercontent.com/kubernetes/dashboard/v2.0.0/aio/deploy/recommended.yaml

kubectl get pod -n kubernetes-dashboard # 检查 pod 是否启动完成

kubectl proxy # 启动代理

http://localhost:8001/api/v1/namespaces/kubernetes-dashboard/services/https:kubernetes-dashboard:/proxy/ # 访问地址
```

- 生成最高权限的 admin 用户

  ```yaml
  kind: ClusterRoleBinding
  apiVersion: rbac.authorization.k8s.io/v1beta1
  metadata:
    name: admin
    annotations:
      rbac.authorization.kubernetes.io/autoupdate: "true"
  roleRef:
    kind: ClusterRole
    name: cluster-admin
    apiGroup: rbac.authorization.k8s.io
  subjects:
  - kind: ServiceAccount
    name: admin
    namespace: kube-system
  ---
  apiVersion: v1
  kind: ServiceAccount
  metadata:
    name: admin
    namespace: kube-system
    labels:
      kubernetes.io/cluster-service: "true"
      addonmanager.kubernetes.io/mode: Reconcile
  ```

  ```bash
  kubectl create -f admin-role.yaml
  ```

  ### 获取token

  ```bash
  kubectl -n kube-system get secret admin-token-nwphb -o jsonpath={.data.token}|base64 -d
  ```

  

  

