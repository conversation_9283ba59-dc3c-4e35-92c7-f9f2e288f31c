---
title: "Linux LVM 分区扩容完整指南"
date: 2023-07-24T14:56:12+08:00
draft: false
tags: [ "linux","lvm","storage"]
tags_weight: 80
categories: ["SRE"]
categories_weight: 80
keywords:
- linux
- lvm
- 分区扩容
- 存储管理
- 磁盘管理
- 文件系统
description: "使用 LVM 逻辑卷管理器进行 Linux 分区动态扩容的详细操作指南"
---

# LVM 概述

## 什么是 LVM

LVM（Logical Volume Manager）是 Linux 系统中的逻辑卷管理器，它在物理存储设备之上提供了一个抽象层，使得存储管理更加灵活和动态。

### LVM 核心概念

- **PV（Physical Volume）**：物理卷，实际的存储设备或分区
- **VG（Volume Group）**：卷组，由一个或多个物理卷组成的存储池
- **LV（Logical Volume）**：逻辑卷，从卷组中分配的逻辑存储单元
- **PE（Physical Extent）**：物理扩展单元，PV 的最小分配单位
- **LE（Logical Extent）**：逻辑扩展单元，LV 的最小分配单位

### LVM 架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   文件系统      │    │   文件系统      │    │   文件系统      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ LV (逻辑卷)     │    │ LV (逻辑卷)     │    │ LV (逻辑卷)     │
├─────────────────┴────┴─────────────────┴────┴─────────────────┤
│                    VG (卷组)                                  │
├─────────────────┬────┬─────────────────┬────┬─────────────────┤
│ PV (物理卷)     │    │ PV (物理卷)     │    │ PV (物理卷)     │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│   /dev/sda1     │    │   /dev/sdb1     │    │   /dev/sdc1     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

# 环境准备

## 系统要求

- Linux 操作系统（CentOS、Ubuntu、RHEL 等）
- 具有 root 权限或 sudo 权限
- 已安装 LVM 工具包

## 安装 LVM 工具

### CentOS/RHEL
```bash
yum install -y lvm2
```

### Ubuntu/Debian
```bash
apt-get update
apt-get install -y lvm2
```

## 检查当前状态

在开始扩容前，先了解当前系统的存储状况：

```bash
# 查看磁盘分区情况
lsblk

# 查看物理卷
pvdisplay

# 查看卷组
vgdisplay

# 查看逻辑卷
lvdisplay

# 查看文件系统使用情况
df -h
```

# 分区扩容操作步骤

## 步骤 1：添加新磁盘并创建分区

### 1.1 识别新磁盘

```bash
# 查看所有磁盘设备
lsblk

# 查看分区表
cat /proc/partitions

# 如果是虚拟机环境，可能需要重新扫描 SCSI 总线
echo "- - -" > /sys/class/scsi_host/host0/scan
echo "- - -" > /sys/class/scsi_host/host1/scan
echo "- - -" > /sys/class/scsi_host/host2/scan
```

### 1.2 创建 LVM 分区

使用 `cfdisk` 创建分区（推荐，图形化界面）：

```bash
# 对新磁盘进行分区
cfdisk /dev/vdb
```

**操作步骤**：
1. 选择 `New` 创建新分区
2. 输入分区大小（默认使用全部空间）
3. 选择 `Type` 并设置为 `8e` (Linux LVM)
4. 选择 `Write` 保存更改
5. 输入 `yes` 确认
6. 选择 `Quit` 退出

或使用 `fdisk` 命令行方式：

```bash
fdisk /dev/vdb
# n (新建分区)
# p (主分区)
# 1 (分区号)
# 回车 (起始扇区，使用默认)
# 回车 (结束扇区，使用默认)
# t (更改分区类型)
# 8e (Linux LVM)
# w (写入并退出)
```

### 1.3 刷新分区表

```bash
# 通知内核重新读取分区表
sudo partprobe /dev/vdb

# 或者使用 udevadm 触发设备事件
sudo udevadm trigger

# 验证分区是否创建成功
cat /proc/partitions
lsblk
```

## 步骤 2：创建物理卷（PV）

```bash
# 创建物理卷
sudo pvcreate /dev/vdb1

# 验证物理卷创建
sudo pvdisplay /dev/vdb1

# 查看所有物理卷
sudo pvscan
```

## 步骤 3：扩展卷组（VG）

### 3.1 查看现有卷组

```bash
# 查看卷组信息
sudo vgdisplay

# 或者只查看卷组名称
sudo vgdisplay | grep "VG Name"

# 查看卷组摘要信息
sudo vgs
```

### 3.2 将物理卷添加到卷组

```bash
# 将新的物理卷添加到现有卷组（假设卷组名为 centos）
sudo vgextend centos /dev/vdb1

# 验证卷组扩展
sudo vgdisplay centos

# 查看可用空间
sudo vgs centos
```

## 步骤 4：扩展逻辑卷（LV）

### 4.1 查看逻辑卷信息

```bash
# 查看逻辑卷详细信息
sudo lvdisplay

# 查看逻辑卷摘要
sudo lvs

# 查看特定逻辑卷
sudo lvdisplay /dev/mapper/centos-root
```

### 4.2 扩展逻辑卷

**方式一：使用所有可用空间**

```bash
# 将卷组中所有可用空间分配给逻辑卷
sudo lvextend -l +100%FREE /dev/mapper/centos-root
```

**方式二：按指定大小扩展**

```bash
# 增加指定大小（例如 20GB）
sudo lvextend -L+20G /dev/mapper/centos-root

# 扩展到指定总大小（例如扩展到 100GB）
sudo lvextend -L100G /dev/mapper/centos-root
```

**方式三：按百分比扩展**

```bash
# 使用卷组 50% 的可用空间
sudo lvextend -l +50%FREE /dev/mapper/centos-root

# 使用卷组 100% 的空间（包括已使用的）
sudo lvextend -l +100%VG /dev/mapper/centos-root
```

### 4.3 验证逻辑卷扩展

```bash
# 查看逻辑卷大小变化
sudo lvdisplay /dev/mapper/centos-root

# 查看块设备信息
lsblk
```

## 步骤 5：扩展文件系统

逻辑卷扩展后，还需要扩展文件系统才能使用新增的空间。

### 5.1 确定文件系统类型

```bash
# 查看文件系统类型
df -T

# 或者使用 blkid
blkid /dev/mapper/centos-root

# 或者使用 lsblk
lsblk -f
```

### 5.2 扩展不同类型的文件系统

**XFS 文件系统**（CentOS 7+ 默认）：

```bash
# 扩展 XFS 文件系统
sudo xfs_growfs /

# 验证扩展结果
df -h /
```

**EXT4 文件系统**（Ubuntu 等系统常用）：

```bash
# 扩展 EXT4 文件系统
sudo resize2fs /dev/mapper/centos-root

# 验证扩展结果
df -h /
```

**EXT3 文件系统**：

```bash
# 扩展 EXT3 文件系统
sudo resize2fs /dev/mapper/centos-root

# 验证扩展结果
df -h /
```

### 5.3 验证最终结果

```bash
# 查看文件系统使用情况
df -h

# 查看完整的存储架构
lsblk

# 查看 LVM 状态摘要
sudo pvs && sudo vgs && sudo lvs
```

# 常见问题与解决方案

## 问题 1：虚拟机无法识别新添加的磁盘

**解决方案**：

```bash
# 方法 1：重新扫描 SCSI 总线
echo "- - -" > /sys/class/scsi_host/host0/scan
echo "- - -" > /sys/class/scsi_host/host1/scan
echo "- - -" > /sys/class/scsi_host/host2/scan

# 方法 2：重启虚拟机
sudo reboot

# 方法 3：使用 rescan-scsi-bus 工具
sudo rescan-scsi-bus.sh
```

## 问题 2：分区表未更新

**解决方案**：

```bash
# 强制重新读取分区表
sudo partprobe /dev/vdb

# 如果上述命令失败，尝试
sudo hdparm -z /dev/vdb

# 或者重启系统
sudo reboot
```

## 问题 3：文件系统扩展失败

**解决方案**：

```bash
# 对于 XFS 文件系统，确保文件系统已挂载
mount | grep xfs

# 对于 EXT 文件系统，可以在线或离线扩展
# 在线扩展（推荐）
sudo resize2fs /dev/mapper/centos-root

# 离线扩展（需要先卸载）
sudo umount /dev/mapper/centos-root
sudo e2fsck -f /dev/mapper/centos-root
sudo resize2fs /dev/mapper/centos-root
sudo mount /dev/mapper/centos-root /
```

## 问题 4：权限不足

**解决方案**：

```bash
# 确保使用 sudo 或 root 权限
sudo su -

# 检查用户是否在相关组中
groups $USER
```

# 最佳实践

## 扩容前的准备工作

1. **数据备份**：在进行任何存储操作前，务必备份重要数据
2. **系统快照**：如果是虚拟机，建议先创建系统快照
3. **停止服务**：对于生产环境，建议在维护窗口期间操作
4. **文档记录**：记录当前的存储配置，便于回滚

## 监控和验证

```bash
# 创建监控脚本
cat > /tmp/storage_monitor.sh << 'EOF'
#!/bin/bash
echo "=== Storage Status Check ==="
echo "Date: $(date)"
echo
echo "=== Disk Usage ==="
df -h
echo
echo "=== Block Devices ==="
lsblk
echo
echo "=== LVM Status ==="
sudo pvs && sudo vgs && sudo lvs
EOF

chmod +x /tmp/storage_monitor.sh
/tmp/storage_monitor.sh
```

## 自动化脚本示例

```bash
#!/bin/bash
# LVM 扩容自动化脚本

set -e

DEVICE="/dev/vdb"
VG_NAME="centos"
LV_PATH="/dev/mapper/centos-root"
MOUNT_POINT="/"

echo "开始 LVM 扩容流程..."

# 1. 创建分区
echo "正在创建分区..."
(
echo n   # 新建分区
echo p   # 主分区
echo 1   # 分区号
echo     # 默认起始扇区
echo     # 默认结束扇区
echo t   # 更改分区类型
echo 8e  # Linux LVM
echo w   # 写入并退出
) | fdisk $DEVICE

# 2. 刷新分区表
partprobe $DEVICE
sleep 2

# 3. 创建物理卷
echo "正在创建物理卷..."
pvcreate ${DEVICE}1

# 4. 扩展卷组
echo "正在扩展卷组..."
vgextend $VG_NAME ${DEVICE}1

# 5. 扩展逻辑卷
echo "正在扩展逻辑卷..."
lvextend -l +100%FREE $LV_PATH

# 6. 扩展文件系统
echo "正在扩展文件系统..."
FS_TYPE=$(blkid -o value -s TYPE $LV_PATH)
case $FS_TYPE in
    xfs)
        xfs_growfs $MOUNT_POINT
        ;;
    ext4|ext3)
        resize2fs $LV_PATH
        ;;
    *)
        echo "不支持的文件系统类型: $FS_TYPE"
        exit 1
        ;;
esac

echo "LVM 扩容完成！"
df -h $MOUNT_POINT
```

# 总结

## LVM 扩容的优势

- **动态扩展**：无需停机即可扩展存储空间
- **灵活管理**：可以跨多个物理设备创建逻辑卷
- **快照功能**：支持创建逻辑卷快照用于备份
- **在线调整**：支持在线扩展和缩减（需谨慎）

## 注意事项

- **备份重要**：任何存储操作前都要备份数据
- **测试环境**：先在测试环境验证操作流程
- **监控空间**：定期监控存储空间使用情况
- **文档记录**：详细记录每次操作的步骤和结果

## 相关命令速查

| 操作 | 命令 |
|------|------|
| 查看磁盘 | `lsblk`, `fdisk -l` |
| 创建分区 | `cfdisk`, `fdisk` |
| 创建 PV | `pvcreate` |
| 查看 PV | `pvdisplay`, `pvs` |
| 扩展 VG | `vgextend` |
| 查看 VG | `vgdisplay`, `vgs` |
| 扩展 LV | `lvextend` |
| 查看 LV | `lvdisplay`, `lvs` |
| 扩展文件系统 | `xfs_growfs`, `resize2fs` |
| 查看使用情况 | `df -h` |

