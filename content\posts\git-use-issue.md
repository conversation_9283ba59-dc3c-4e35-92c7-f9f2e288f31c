---
title: "Git 使用问题的记录"
date: 2021-04-25T10:35:57+08:00
draft: false
tags: [ "git"]
tags_weight: 60
categories: ["devops","linux"]
categories_weight: 60
keywords:
- git
- 使用
- 高级用法
- Devops
description: "git 高级用法的使用记录"
---



## **指定特定仓库拉取代码**

```bash
git remote add origin https://github.com/demo/example.git
git pull https://repo.csheidou.com/forestry_afterend/prevention_fire_test.git master
git branch --set-upstream-to=origin/master
git status
git pull 
```



## **更改远程仓库地址**

```bash
git remote -v 
git remote set-url origin  https://github.com/demo/example.git
git pull origin master  --allow-unrelated-histories
git push 
git branch --set-upstream-to=origin/master
```



## **清理本地错误的用户信息**

```bash
运行一下命令缓存输入的用户名和密码：
git config --global credential.helper wincred

清除掉缓存在git中的用户名和密码
git credential-manager uninstall
```



## **解决 Windows git pull 卡住不动问题**

> 原因 在 git clone 时后面仓库地址是非公开的 需要验证用户及密码 而windows 默认是已弹窗形式 
>
> 解决方法：`取消窗口拉取`

```bash
git config --system --unset credential.helper
```



## **解决 git 每次 pull/push 多需要输入账号密码问题**

```bash
git config --global credential.helper store
```



## **重置 commit id**

```bash
git rebase -i HEAD~3  # 表示需要修改的是最近3次, 此操作会打开一个编辑窗口。 然后按`i`编辑，把 pick 改成 edit，按'Esc'退出编辑，按`:wq`保存退出
git commit --amend -s # 打开刚才更改为 edit 的commit，修改为自己想要的
git rebase --continue 
git log
git push -f
```
