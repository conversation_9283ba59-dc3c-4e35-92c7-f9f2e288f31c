<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="hugoGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00b894;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00a085;stop-opacity:1" />
    </linearGradient>
    <filter id="glow2">
      <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <rect width="800" height="400" fill="url(#hugoGrad)"/>
  
  <!-- Hugo logo inspired design -->
  <circle cx="400" cy="200" r="80" fill="#ffffff" opacity="0.9" filter="url(#glow2)"/>
  <polygon points="400,140 440,180 400,220 360,180" fill="#ff6b6b" filter="url(#glow2)"/>
  
  <!-- Code brackets -->
  <text x="200" y="250" font-family="monospace" font-size="48" fill="#ffffff" opacity="0.7">&lt;</text>
  <text x="580" y="250" font-family="monospace" font-size="48" fill="#ffffff" opacity="0.7">/&gt;</text>
  
  <!-- Decorative dots -->
  <circle cx="100" cy="100" r="8" fill="#ffffff" opacity="0.5"/>
  <circle cx="700" cy="100" r="6" fill="#ffffff" opacity="0.6"/>
  <circle cx="100" cy="300" r="10" fill="#ffffff" opacity="0.4"/>
  <circle cx="700" cy="300" r="7" fill="#ffffff" opacity="0.5"/>
  
  <text x="400" y="350" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#ffffff">Hugo Guide</text>
</svg>
