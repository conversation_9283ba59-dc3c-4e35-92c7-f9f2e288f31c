// 苹果窗口风格代码框复制功能
document.addEventListener('DOMContentLoaded', function() {
    initCodeCopy();
});

function initCodeCopy() {
    // 为所有代码块添加复制按钮
    const codeBlocks = document.querySelectorAll('.highlight pre');
    
    codeBlocks.forEach(function(codeBlock) {
        // 检查是否已经有复制按钮
        if (codeBlock.parentElement.querySelector('.copy-button')) {
            return;
        }
        
        // 创建复制按钮
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.textContent = 'COPY';
        copyButton.setAttribute('aria-label', '复制代码');
        copyButton.setAttribute('title', '复制代码');
        
        // 添加点击事件
        copyButton.addEventListener('click', function() {
            copyCodeToClipboard(codeBlock, copyButton);
        });
        
        // 将按钮添加到代码块容器
        codeBlock.parentElement.appendChild(copyButton);
    });
    
    // 为代码块添加语言标签
    addLanguageLabels();
}

function copyCodeToClipboard(codeBlock, button) {
    // 获取代码文本
    let codeText = '';
    
    // 尝试从 code 元素获取文本
    const codeElement = codeBlock.querySelector('code');
    if (codeElement) {
        // 移除行号和其他不需要的元素
        const lines = codeElement.querySelectorAll('.line');
        if (lines.length > 0) {
            // 如果有行结构，逐行提取
            lines.forEach(line => {
                const cl = line.querySelector('.cl');
                if (cl) {
                    codeText += cl.textContent + '\n';
                }
            });
            // 移除最后的换行符
            codeText = codeText.replace(/\n$/, '');
        } else {
            // 直接获取代码文本
            codeText = codeElement.textContent;
        }
    } else {
        // 直接从 pre 元素获取
        codeText = codeBlock.textContent;
    }
    
    // 清理代码文本
    codeText = codeText.trim();
    
    // 复制到剪贴板
    if (navigator.clipboard && window.isSecureContext) {
        // 使用现代 Clipboard API
        navigator.clipboard.writeText(codeText).then(function() {
            showCopySuccess(button);
        }).catch(function(err) {
            console.error('复制失败:', err);
            fallbackCopyTextToClipboard(codeText, button);
        });
    } else {
        // 降级方案
        fallbackCopyTextToClipboard(codeText, button);
    }
}

function fallbackCopyTextToClipboard(text, button) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    
    // 避免滚动到底部
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess(button);
        } else {
            console.error('复制命令失败');
        }
    } catch (err) {
        console.error('复制失败:', err);
    }
    
    document.body.removeChild(textArea);
}

function showCopySuccess(button) {
    const originalText = button.textContent;
    button.textContent = 'COPIED';
    button.classList.add('copied');
    
    // 2秒后恢复原状
    setTimeout(function() {
        button.textContent = originalText;
        button.classList.remove('copied');
    }, 2000);
}

function addLanguageLabels() {
    const highlights = document.querySelectorAll('.highlight');
    
    highlights.forEach(function(highlight) {
        const codeElement = highlight.querySelector('code[data-lang]');
        if (codeElement) {
            const lang = codeElement.getAttribute('data-lang');
            if (lang) {
                highlight.setAttribute('data-lang', lang);
            }
        }
    });
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + Shift + C 复制当前聚焦的代码块
    if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'C') {
        const focusedElement = document.activeElement;
        const codeBlock = focusedElement.closest('.highlight');
        if (codeBlock) {
            e.preventDefault();
            const copyButton = codeBlock.querySelector('.copy-button');
            if (copyButton) {
                copyButton.click();
            }
        }
    }
});

// 添加焦点支持，提高可访问性
function addFocusSupport() {
    const codeBlocks = document.querySelectorAll('.highlight pre');
    
    codeBlocks.forEach(function(codeBlock) {
        // 使代码块可聚焦
        codeBlock.setAttribute('tabindex', '0');
        codeBlock.setAttribute('role', 'region');
        codeBlock.setAttribute('aria-label', '代码块');
        
        // 添加焦点样式
        codeBlock.addEventListener('focus', function() {
            this.parentElement.style.outline = '2px solid #007acc';
            this.parentElement.style.outlineOffset = '2px';
        });
        
        codeBlock.addEventListener('blur', function() {
            this.parentElement.style.outline = '';
            this.parentElement.style.outlineOffset = '';
        });
    });
}

// 初始化焦点支持
document.addEventListener('DOMContentLoaded', function() {
    addFocusSupport();
});

// 处理动态添加的代码块
const observer = new MutationObserver(function(mutations) {
    mutations.forEach(function(mutation) {
        if (mutation.type === 'childList') {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    const newCodeBlocks = node.querySelectorAll ? node.querySelectorAll('.highlight pre') : [];
                    if (newCodeBlocks.length > 0) {
                        // 为新添加的代码块初始化功能
                        setTimeout(function() {
                            initCodeCopy();
                            addFocusSupport();
                        }, 100);
                    }
                }
            });
        }
    });
});

// 开始观察
observer.observe(document.body, {
    childList: true,
    subtree: true
});
