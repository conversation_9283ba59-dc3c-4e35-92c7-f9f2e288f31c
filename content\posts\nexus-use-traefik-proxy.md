---
title: "使用 Docker Compose 部署 Nexus3 Docker 私服并配置 Traefik 代理"
date: 2021-06-10T09:51:36+08:00
draft: false
tags: ["nexus3","traefik","mkcert","https"]
tags_weight: 20
categories: ["devops","docker-compose","proxy"]
categories_weight: 20
keywords:
- nexus3
- traefik
- https
- tls
- devops
- docker-compose
- proxy
- nginx
- 私服
description: "使用 Docker Compose 一键部署 Nexus3 私服，并配置 Traefik 进行代理管理"
---

# 环境说明

## 软件版本

- 操作系统：CentOS 7.9.2009
- Docker 版本：20.10.7
- Docker Compose 版本：1.18.0
- Mkcert 版本：v1.4.3

## 端口规划

### Nexus 私服端口规划

| 私服名称      | 私服作用                               | 私服类型 | 私服端口 |
| ------------- | -------------------------------------- | -------- | -------- |
| docker-custom | 存放自定义 push 的镜像，与项目环境无关 | hosted   | 8086     |
| idocker.io    | 代理仓库和 custom 仓库的集合           | group    | 8082     |
| docker-dev    | 存放项目 dev 环境镜像                  | hosted   | 8083     |
| docker-qa     | 存放项目 qa 环境镜像                   | hosted   | 8084     |
| docker-prod   | 存放项目 prod 环境镜像                 | hosted   | 8085     |

> 除了上述 Docker 私服端口外，还有 Nexus3 管理面板端口 8081

### Docker 代理仓库列表

| 名称            | 私服类型 | 说明                                                       | 地址                                 |
| --------------- | -------- | ---------------------------------------------------------- | ------------------------------------ |
| docker-google   | proxy    | Google 公开镜像（需要科学上网）                            | https://gcr.io                       |
| docker-k8s      | proxy    | Kubernetes 官方 Google 镜像源（需要科学上网）              | https://k8s.gcr.io                   |
| docker-aliyun   | proxy    | 阿里云同步 Docker 官方源（存在部分镜像未同步问题）         | https://7bezldxe.mirror.aliyuncs.com |
| docker-official | proxy    | Docker Hub 官方镜像地址（限制带宽：匿名用户100次，认证用户200次） | https://registry-1.docker.io         |

### Traefik Ingress 端口规划

| Ingress 名称 | Ingress 作用          | Ingress 端口 |
| ------------ | --------------------- | ------------ |
| http         | HTTP 站点入口         | 80           |
| https        | HTTPS TLS 证书站点入口 | 443          |

# 使用 Docker Compose 一键部署私服和 Traefik

本次部署所依赖的工具和配置文件已整理至 [GitHub 仓库](https://github.com/cdryzun/traefik-nexus.git) 中，文档中的步骤均已在 CentOS 7 系统中验证通过。其他平台及架构只需替换为相应的软件包版本即可。

## Docker Compose 安装和准备工作

```bash
yum install -y docker-compose # CentOS 下直接使用 yum 安装
```



## 克隆代码

```bash
git clone https://github.com/cdryzun/traefik-nexus.git
```

## 生成证书

> ⚠️ **注意**：使用 mkcert 生成证书时，`cert-file` 和 `key-file` 的文件名称有特定要求。例如为域名 `example.io` 生成证书文件时，文件前缀应该为 `example-io`，否则会导致 Traefik 在引用证书时匹配不上，从而使用默认证书。

**mkcert 版本列表请[查看](https://github.com/FiloSottile/mkcert/releases)。**

```bash
cd traefik-nexus

chmod a+x ./mkcert # Linux amd64 架构，请确认与操作系统架构相同

./mkcert --version
v1.4.3

mkdir -p certs # 创建存放证书的文件夹

# 生成域名证书
./mkcert -ecdsa -cert-file certs/treesir-pub.crt -key-file certs/treesir-pub.key "treesir.pub" "*.treesir.pub" "treesir.lan" "*.treesir.lan"

# 生成 Docker 私服使用证书
./mkcert -ecdsa -cert-file certs/idocker-io.crt -key-file certs/idocker-io.key "idocker.io" "*.idocker.io" "idocker.lan" "*.idocker.lan"

# 确认证书是否存在
ls certs/
idocker-io.crt  idocker-io.key  treesir-pub.crt  treesir-pub.key
```

生成证书后，检查确认 `traefik/traefik.yml` 配置文件中的配置是否对应：

```bash
cat traefik/traefik.yml
...
tls:
  certificates: # 将生成的证书加到 Traefik TLS 证书池，自动匹配
    - certFile: /certs/treesir-pub.crt
      keyFile: /certs/treesir-pub.key
  certificates:
    - certFile: /certs/idocker-io.crt
      keyFile: /certs/idocker-io.key
  options:
    default:
      minVersion: VersionTLS12
      cipherSuites:
        - TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256
        - TLS_RSA_WITH_AES_256_GCM_SHA384
  stores: # 配置默认证书
    default:
      defaultCertificate:
        certFile: /certs/treesir-pub.crt
        keyFile: /certs/treesir-pub.key
...
```

## 检查 Docker Compose 部署文件

```bash
cat docker-compose.yml
version: "3"
services:
  traefik:
    restart: always
    image: traefik:v2.4.8
    container_name: traefik
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./certs:/certs:ro
      - ./traefik/traefik.yml:/traefik.yml:ro
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik.entrypoints=http,https
      - traefik.http.routers.traefik.rule=Host(`traefik.treesir.pub`)  # Traefik Dashboard 关联域名
      - traefik.http.routers.traefik.tls=true
      - traefik.http.routers.traefik.service=api@internal
  nexus:
    restart: always
    image: sonatype/nexus3:3.30.1
    container_name: nexus3
    privileged: true
    environment:
      - INSTALL4J_ADD_VM_PARAMS=-Xms4g -Xmx4g -XX:MaxDirectMemorySize=6g # JVM 参数，针对 8G 内存机器，按比例和实际需求调整
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./nexus-data:/nexus-data
    labels:
      - traefik.enable=true
      - traefik.http.routers.1.entrypoints=http,https
      - traefik.http.routers.1.rule=Host(`mirror.treesir.pub`) && PathPrefix(`/`)
      - traefik.http.routers.1.tls=true
      - traefik.http.routers.1.service=one
      - traefik.http.services.one.loadbalancer.server.port=8081
      - traefik.http.routers.2.entrypoints=http,https
      - traefik.http.routers.2.rule=Host(`idocker.io`) || (Host(`idocker.io`) && Method(`GET`))
      - traefik.http.routers.2.tls=true
      - traefik.http.routers.2.service=two
      - traefik.http.services.two.loadbalancer.server.port=8082
      - traefik.http.routers.3.entrypoints=http,https
      - traefik.http.routers.3.rule=(Host(`idocker.io`) && Path(`/v1/search`)) || (Host(`idocker.io`) && Method(`PUT`,`HEAD`,`POST`,`PATCH`))
      - traefik.http.routers.3.priority=100
      - traefik.http.routers.3.tls=true
      - traefik.http.routers.3.service=three
      - traefik.http.services.three.loadbalancer.server.port=8086
      - traefik.http.routers.4.rule=Host(`dev.idocker.io`) && PathPrefix(`/`)
      - traefik.http.routers.4.tls=true
      - traefik.http.routers.4.service=four
      - traefik.http.services.four.loadbalancer.server.port=8083
      - traefik.http.routers.5.rule=Host(`qa.idocker.io`) && PathPrefix(`/`)
      - traefik.http.routers.5.tls=true
      - traefik.http.routers.5.service=five
      - traefik.http.services.five.loadbalancer.server.port=8084
      - traefik.http.routers.6.rule=Host(`prod.idocker.io`) && PathPrefix(`/`)
      - traefik.http.routers.6.tls=true
      - traefik.http.routers.6.service=six
      - traefik.http.services.six.loadbalancer.server.port=8085
networks:
  default:
    external:
      name: docker
```



## 启动服务并导入 CA 证书

### 使用 Docker Compose 启动服务

```bash
# 创建 Nexus 数据存储目录，开放权限防止报错
mkdir -p ./nexus-data
chmod 777 -R ./nexus-data

# 创建网络
docker network create docker

# 启动服务
docker-compose up -d

# 检查日志，确认有无错误
docker-compose logs -f
```

### 添加 Hosts 记录

```bash
# macOS 系统
sudo -- sh -c "echo '************ idocker.io dev.idocker.io qa.idocker.io prod.idocker.io mirror.treesir.pub traefik.treesir.pub' >> /etc/hosts"
```

### 导入 CA 证书并添加信任

```bash
yum install -y lrzsz

sz -y $(mkcert -CAROOT)/rootCA.pem
```

双击证书文件，添加到系统中：

![image-20210610110703249](https://cdn.treesir.pub/img/image-20210610110703249.png)

双击打开，设置为永远信任：

![image-20210610110824559](https://cdn.treesir.pub/img/image-20210610110824559.png)



![image-20210610110900639](https://cdn.treesir.pub/img/image-20210610110900639.png)

![image-20210610110926368](https://cdn.treesir.pub/img/image-20210610110926368.png)

测试访问 Nexus Dashboard 管理界面：

![image-20210610111116798](https://cdn.treesir.pub/img/image-20210610111116798.png)

查看 Nexus Dashboard 初始化 admin 密码：

```bash
docker exec nexus3 bash -c "cat /nexus-data/admin.password"
```

# Docker 私服测试使用

> 在测试之前，请检查是否已创建对应的 Docker 私服仓库，且对应端口正常监听。由于此部分之前已有相关整理，故此处省略，**参考步骤请查看[文档](https://www.treesir.pub/post/helm-k8s-deploy-nexus/#%E7%A7%81%E6%9C%8D%E5%88%9B%E5%BB%BA)**。

## 检查配置

创建 `idocker.io` 可供测试使用的仓库列表：

![image-20210610113856665](https://cdn.treesir.pub/img/image-20210610113856665.png)

Traefik 中的配置检查：

![image-20210610114024241](https://cdn.treesir.pub/img/image-20210610114024241.png)

## 信任 Docker 私服

```bash
cat /etc/docker/daemon.json
{
...
    "insecure-registries": ["idocker.io"]
...
}

docker login idocker.io
```

![image-20210610122651706](https://cdn.treesir.pub/img/image-20210610122651706.png)

## 查看私服镜像情况

`idocker.io` Group 私服：

![image-20210610122756789](https://cdn.treesir.pub/img/image-20210610122756789.png)

`docker-custom` Hosted 私服：

![image-20210610122836249](https://cdn.treesir.pub/img/image-20210610122836249.png)

# 总结

通过本教程，我们成功实现了：

1. 使用 Docker Compose 一键部署 Nexus3 Docker 私服
2. 配置 Traefik 作为反向代理，实现多域名和端口的统一管理
3. 使用 mkcert 生成本地 HTTPS 证书，确保安全访问
4. 配置多环境的 Docker 私服仓库（dev、qa、prod）
5. 实现了私服的 HTTPS 访问和证书自动匹配

这套方案提供了完整的企业级 Docker 私服解决方案，支持多环境隔离和安全的 HTTPS 访问。

# 参考文档

- [Traefik v1 到 v2 迁移指南](https://doc.traefik.io/traefik/migration/v1-to-v2/)
- [Traefik 快速开始](https://shanyue.tech/op/traefik.html#%E5%BF%AB%E9%80%9F%E5%BC%80%E5%A7%8B)
- [Traefik Docker Compose ACME TLS 用户指南](https://doc.traefik.io/traefik/user-guides/docker-compose/acme-tls/)
- [Traefik Local 项目](https://github.com/SushiFu/traefik-local)
- [Traefik 2 TLS 101](https://traefik.io/blog/traefik-2-tls-101-23b4fbee81f1/)
