/* ===== 站点标题字体大小优化 ===== */

/* 导航栏站点标题优化 - 更精确的选择器 */
.main-menu .flex.space-x-3 a[href="/"],
.main-menu nav a[href="/"] {
  font-size: 2rem !important;
  font-weight: 800 !important;
  color: #1f2937 !important;
  text-decoration: none !important;
  transition: all 0.3s ease !important;
  margin-left: -40px !important;
}

/* 深色模式下的标题颜色 */
.dark .main-menu .flex.space-x-3 a[href="/"],
.dark .main-menu nav a[href="/"] {
  color: #f9fafb !important;
}

/* 鼠标悬停效果 */
.main-menu .flex.space-x-3 a[href="/"]:hover,
.main-menu nav a[href="/"]:hover {
  color: #3b82f6 !important;
  transform: translateY(-1px) !important;
}

.dark .main-menu .flex.space-x-3 a[href="/"]:hover,
.dark .main-menu nav a[href="/"]:hover {
  color: #60a5fa !important;
}

/* 大屏幕设备优化 */
@media (min-width: 1280px) {
  .main-menu .flex.space-x-3 a[href="/"],
  .main-menu nav a[href="/"] {
    font-size: 2.25rem !important;
  }
}

/* 平板设备适配 */
@media (min-width: 768px) and (max-width: 1279px) {
  .main-menu .flex.space-x-3 a[href="/"],
  .main-menu nav a[href="/"] {
    font-size: 1.875rem !important;
  }
}

/* 移动端适配 */
@media (max-width: 767px) {
  .main-menu .flex.space-x-3 a[href="/"],
  .main-menu nav a[href="/"] {
    font-size: 1.5rem !important;
  }
}

/* 确保标题在导航栏中垂直居中 */
.main-menu nav,
.main-menu .flex.space-x-3 {
  display: flex;
  align-items: center;
}

/* 添加渐变效果（可选） */
.main-menu .flex.space-x-3 a[href="/"],
.main-menu nav a[href="/"] {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .main-menu .flex.space-x-3 a[href="/"],
.dark .main-menu nav a[href="/"] {
  background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 悬停时的渐变效果 */
.main-menu .flex.space-x-3 a[href="/"]:hover,
.main-menu nav a[href="/"]:hover {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .main-menu .flex.space-x-3 a[href="/"]:hover,
.dark .main-menu nav a[href="/"]:hover {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* ===== 苹果窗口风格代码框优化 ===== */

/* 代码框容器 - 苹果窗口风格 */
.highlight {
  position: relative;
  margin: 1.5rem 0;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.1);
  background: #ffffff;
  transition: all 0.3s ease;
}

/* 深色模式代码框 */
.dark .highlight {
  background: #1e1e1e;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 代码框悬停效果 */
.highlight:hover {
  transform: translateY(-1px);
  border-color: rgba(0, 0, 0, 0.15);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.dark .highlight:hover {
  border-color: rgba(255, 255, 255, 0.25);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* 苹果窗口标题栏 */
.highlight::before {
  content: '';
  display: block;
  height: 32px;
  background: linear-gradient(180deg, #f5f5f5 0%, #e8e8e8 100%);
  border-bottom: 1px solid #d1d1d1;
  position: relative;
}

/* 深色模式标题栏 */
.dark .highlight::before {
  background: linear-gradient(180deg, #3c3c3c 0%, #2d2d2d 100%);
  border-bottom: 1px solid #404040;
}

/* 苹果窗口控制按钮 */
.highlight::after {
  content: '';
  position: absolute;
  top: 10px;
  left: 12px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff5f57;
  box-shadow:
    20px 0 0 #ffbd2e,
    40px 0 0 #28ca42,
    0 0 0 1px rgba(0, 0, 0, 0.1),
    20px 0 0 1px rgba(0, 0, 0, 0.1),
    40px 0 0 1px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

/* 深色模式控制按钮 */
.dark .highlight::after {
  background: #ff5f57;
  box-shadow:
    20px 0 0 #ffbd2e,
    40px 0 0 #28ca42,
    0 0 0 1px rgba(0, 0, 0, 0.3),
    20px 0 0 1px rgba(0, 0, 0, 0.3),
    40px 0 0 1px rgba(0, 0, 0, 0.3);
}

/* 代码块本体 */
.highlight pre {
  margin: 0;
  padding: 16px 20px 20px 20px;
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  text-shadow: none !important;
  font-family: var(--font-mono);
  font-size: 14px;
  line-height: 1.8;  /* 增加行高解决字体重叠 */
  overflow-x: auto;
  color: #24292e;
  font-weight: 400;  /* 确保字体粗细一致 */
  letter-spacing: 0.02em;  /* 轻微增加字符间距 */
  word-spacing: 0.1em;  /* 增加单词间距 */
}

/* 确保代码块内所有元素都没有阴影和重叠 */
.highlight pre *,
.highlight .chroma *,
.highlight code *,
.highlight .chroma .line,
.highlight .chroma .cl {
  text-shadow: none !important;
  box-shadow: none !important;
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
  font-weight: inherit !important;
  line-height: inherit !important;
}

/* 深色模式代码块 */
.dark .highlight pre {
  color: #e1e4e8;
}

/* 深色模式下确保没有阴影 */
.dark .highlight pre *,
.dark .highlight .chroma *,
.dark .highlight code *,
.dark .highlight .chroma .line,
.dark .highlight .chroma .cl {
  text-shadow: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* 代码块滚动条优化 */
.highlight pre::-webkit-scrollbar {
  height: 8px;
}

.highlight pre::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.highlight pre::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.highlight pre::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 深色模式滚动条 */
.dark .highlight pre::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
}

.dark .highlight pre::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .highlight pre::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 语言标签样式 */
.highlight[data-lang]::before {
  content: attr(data-lang);
  position: absolute;
  top: 6px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  z-index: 3;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 深色模式语言标签 */
.dark .highlight[data-lang]::before {
  background: rgba(255, 255, 255, 0.8);
  color: #1e1e1e;
}

/* 检测语言并添加对应的标签 */
.highlight .language-bash::before,
.highlight .chroma .language-bash::before {
  content: 'BASH';
}

.highlight .language-javascript::before,
.highlight .chroma .language-javascript::before {
  content: 'JS';
}

.highlight .language-typescript::before,
.highlight .chroma .language-typescript::before {
  content: 'TS';
}

.highlight .language-python::before,
.highlight .chroma .language-python::before {
  content: 'PYTHON';
}

.highlight .language-go::before,
.highlight .chroma .language-go::before {
  content: 'GO';
}

.highlight .language-rust::before,
.highlight .chroma .language-rust::before {
  content: 'RUST';
}

.highlight .language-yaml::before,
.highlight .chroma .language-yaml::before {
  content: 'YAML';
}

.highlight .language-toml::before,
.highlight .chroma .language-toml::before {
  content: 'TOML';
}

.highlight .language-json::before,
.highlight .chroma .language-json::before {
  content: 'JSON';
}

.highlight .language-html::before,
.highlight .chroma .language-html::before {
  content: 'HTML';
}

.highlight .language-css::before,
.highlight .chroma .language-css::before {
  content: 'CSS';
}

.highlight .language-scss::before,
.highlight .chroma .language-scss::before {
  content: 'SCSS';
}

.highlight .language-markdown::before,
.highlight .chroma .language-markdown::before {
  content: 'MD';
}

.highlight .language-sql::before,
.highlight .chroma .language-sql::before {
  content: 'SQL';
}

.highlight .language-dockerfile::before,
.highlight .chroma .language-dockerfile::before {
  content: 'DOCKER';
}

/* 复制按钮样式 */
.highlight .copy-button {
  position: absolute;
  top: 40px;
  right: 12px;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  border: none;
  border-radius: 6px;
  padding: 6px 10px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 3;
  opacity: 0;
  transform: translateY(-4px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.highlight:hover .copy-button {
  opacity: 1;
  transform: translateY(0);
}

.highlight .copy-button:hover {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.highlight .copy-button:active {
  transform: scale(0.95);
}

/* 深色模式复制按钮 */
.dark .highlight .copy-button {
  background: rgba(255, 255, 255, 0.8);
  color: #1e1e1e;
}

.dark .highlight .copy-button:hover {
  background: rgba(255, 255, 255, 0.9);
}

/* 复制成功状态 */
.highlight .copy-button.copied {
  background: #28ca42 !important;
  color: #ffffff !important;
}

.highlight .copy-button.copied::after {
  content: ' ✓';
}

/* 行号样式优化 */
.highlight .chroma .lnt,
.highlight .chroma .ln {
  color: rgba(0, 0, 0, 0.4);
  user-select: none;
  margin-right: 20px;  /* 增加右边距避免重叠 */
  padding-right: 8px;  /* 添加内边距 */
  font-size: 12px;
  min-width: 2em;  /* 确保行号有足够宽度 */
  text-align: right;  /* 右对齐行号 */
  display: inline-block;
  font-weight: 400;
  line-height: inherit;
  text-shadow: none !important;
}

.dark .highlight .chroma .lnt,
.dark .highlight .chroma .ln {
  color: rgba(255, 255, 255, 0.4);
}

/* 语法高亮颜色优化 - GitHub 风格 */
.highlight .chroma .k,  /* 关键字 */
.highlight .chroma .kd, /* 关键字声明 */
.highlight .chroma .kn, /* 关键字命名空间 */
.highlight .chroma .kp, /* 关键字伪 */
.highlight .chroma .kr, /* 关键字保留 */
.highlight .chroma .kt { /* 关键字类型 */
  color: #d73a49;
  font-weight: 600;
  text-shadow: none !important;
  background: transparent !important;
}

.highlight .chroma .s,  /* 字符串 */
.highlight .chroma .s1, /* 字符串单引号 */
.highlight .chroma .s2, /* 字符串双引号 */
.highlight .chroma .sb, /* 字符串反引号 */
.highlight .chroma .sc, /* 字符串字符 */
.highlight .chroma .sd, /* 字符串文档 */
.highlight .chroma .se, /* 字符串转义 */
.highlight .chroma .sh, /* 字符串heredoc */
.highlight .chroma .si, /* 字符串插值 */
.highlight .chroma .sx, /* 字符串其他 */
.highlight .chroma .sr { /* 字符串正则 */
  color: #032f62;
}

.highlight .chroma .c,  /* 注释 */
.highlight .chroma .c1, /* 注释单行 */
.highlight .chroma .cm, /* 注释多行 */
.highlight .chroma .cp, /* 注释预处理 */
.highlight .chroma .cs { /* 注释特殊 */
  color: #6a737d;
  font-style: italic;
}

.highlight .chroma .m,  /* 数字 */
.highlight .chroma .mb, /* 数字二进制 */
.highlight .chroma .mf, /* 数字浮点 */
.highlight .chroma .mh, /* 数字十六进制 */
.highlight .chroma .mi, /* 数字整数 */
.highlight .chroma .mo { /* 数字八进制 */
  color: #005cc5;
}

.highlight .chroma .n,  /* 名称 */
.highlight .chroma .na, /* 名称属性 */
.highlight .chroma .nb, /* 名称内置 */
.highlight .chroma .nc, /* 名称类 */
.highlight .chroma .nd, /* 名称装饰器 */
.highlight .chroma .ne, /* 名称异常 */
.highlight .chroma .nf, /* 名称函数 */
.highlight .chroma .ni, /* 名称实体 */
.highlight .chroma .nl, /* 名称标签 */
.highlight .chroma .nn, /* 名称命名空间 */
.highlight .chroma .no, /* 名称常量 */
.highlight .chroma .nt, /* 名称标签 */
.highlight .chroma .nv, /* 名称变量 */
.highlight .chroma .nx { /* 名称其他 */
  color: #24292e;
}

.highlight .chroma .o,  /* 操作符 */
.highlight .chroma .ow { /* 操作符单词 */
  color: #d73a49;
  font-weight: 600;
}

/* 深色模式语法高亮 */
.dark .highlight .chroma .k,
.dark .highlight .chroma .kd,
.dark .highlight .chroma .kn,
.dark .highlight .chroma .kp,
.dark .highlight .chroma .kr,
.dark .highlight .chroma .kt {
  color: #ff7b72;
}

.dark .highlight .chroma .s,
.dark .highlight .chroma .s1,
.dark .highlight .chroma .s2,
.dark .highlight .chroma .sb,
.dark .highlight .chroma .sc,
.dark .highlight .chroma .sd,
.dark .highlight .chroma .se,
.dark .highlight .chroma .sh,
.dark .highlight .chroma .si,
.dark .highlight .chroma .sx,
.dark .highlight .chroma .sr {
  color: #a5d6ff;
}

.dark .highlight .chroma .c,
.dark .highlight .chroma .c1,
.dark .highlight .chroma .cm,
.dark .highlight .chroma .cp,
.dark .highlight .chroma .cs {
  color: #8b949e;
}

.dark .highlight .chroma .m,
.dark .highlight .chroma .mb,
.dark .highlight .chroma .mf,
.dark .highlight .chroma .mh,
.dark .highlight .chroma .mi,
.dark .highlight .chroma .mo {
  color: #79c0ff;
}

.dark .highlight .chroma .n,
.dark .highlight .chroma .na,
.dark .highlight .chroma .nb,
.dark .highlight .chroma .nc,
.dark .highlight .chroma .nd,
.dark .highlight .chroma .ne,
.dark .highlight .chroma .nf,
.dark .highlight .chroma .ni,
.dark .highlight .chroma .nl,
.dark .highlight .chroma .nn,
.dark .highlight .chroma .no,
.dark .highlight .chroma .nt,
.dark .highlight .chroma .nv,
.dark .highlight .chroma .nx {
  color: #e1e4e8;
}

.dark .highlight .chroma .o,
.dark .highlight .chroma .ow {
  color: #ff7b72;
}

/* 统一重置所有语法高亮元素的样式 */
.highlight .chroma span,
.highlight .chroma .line span,
.highlight .chroma .cl span {
  text-shadow: none !important;
  box-shadow: none !important;
  background: transparent !important;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  word-spacing: inherit;
}

/* 深色模式下的统一重置 */
.dark .highlight .chroma span,
.dark .highlight .chroma .line span,
.dark .highlight .chroma .cl span {
  text-shadow: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .highlight {
    margin: 1rem -1rem;
    border-radius: 0;
  }

  .highlight::before {
    height: 28px;
  }

  .highlight::after {
    top: 8px;
    left: 10px;
    width: 10px;
    height: 10px;
    box-shadow:
      16px 0 0 #ffbd2e,
      32px 0 0 #28ca42,
      0 0 0 1px rgba(0, 0, 0, 0.1),
      16px 0 0 1px rgba(0, 0, 0, 0.1),
      32px 0 0 1px rgba(0, 0, 0, 0.1);
  }

  .highlight pre {
    padding: 12px 16px 16px 16px;
    font-size: 13px;
    line-height: 1.7;  /* 移动端稍微减少行高 */
  }

  /* 移动端行号优化 */
  .highlight .chroma .lnt,
  .highlight .chroma .ln {
    margin-right: 12px;
    padding-right: 6px;
    font-size: 11px;
  }

  .highlight[data-lang]::before {
    top: 4px;
    right: 10px;
    font-size: 10px;
    padding: 3px 6px;
  }

  .highlight .copy-button {
    top: 32px;
    right: 10px;
    padding: 4px 8px;
    font-size: 10px;
  }
}

/* 平板设备优化 */
@media (max-width: 1024px) and (min-width: 769px) {
  .highlight pre {
    font-size: 13px;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .highlight {
    border: 2px solid;
  }

  .highlight::before {
    border-bottom-width: 2px;
  }

  .highlight .chroma .k,
  .highlight .chroma .kd,
  .highlight .chroma .kn,
  .highlight .chroma .kp,
  .highlight .chroma .kr,
  .highlight .chroma .kt {
    font-weight: 700;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .highlight,
  .highlight .copy-button {
    transition: none !important;
  }

  .highlight:hover {
    transform: none !important;
  }

  .highlight .copy-button:hover {
    transform: none !important;
  }
}

/* ===== 优雅的回到顶部按钮优化 ===== */

/* 回到顶部按钮容器优化 - 强制覆盖主题设置和JavaScript隐藏 */
#top-scroller {
  position: fixed !important;
  bottom: 2rem !important;
  right: 2rem !important;
  left: auto !important;
  top: auto !important;
  width: 60px !important;
  height: 60px !important;
  z-index: 50 !important;
  pointer-events: auto !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 强制覆盖JavaScript的hidden属性 */
#top-scroller[hidden] {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 回到顶部按钮主体样式 */
#top-scroller a {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border: 2px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 50% !important;
  box-shadow:
    0 8px 32px rgba(59, 130, 246, 0.3),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  font-size: 1.5rem !important;
  color: #ffffff !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  text-decoration: none !important;
  position: static !important;
  overflow: hidden !important;
  margin: 0 !important;
  padding: 0 !important;
  pointer-events: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 按钮内部光效 */
#top-scroller a::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.6s ease;
  opacity: 0;
}

/* 悬停效果 */
#top-scroller a:hover {
  transform: translateY(-4px) scale(1.05) !important;
  background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%) !important;
  box-shadow:
    0 12px 40px rgba(59, 130, 246, 0.4),
    0 8px 24px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.3) !important;
}

#top-scroller a:hover::before {
  opacity: 1;
  animation: shimmer 0.6s ease-out;
}

/* 按下效果 */
#top-scroller a:active {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* 深色模式优化 */
.dark #top-scroller a {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%) !important;
  border-color: rgba(148, 163, 184, 0.3) !important;
  box-shadow:
    0 8px 32px rgba(15, 23, 42, 0.6),
    0 4px 16px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(148, 163, 184, 0.2) !important;
  color: #e2e8f0 !important;
}

.dark #top-scroller a:hover {
  background: linear-gradient(135deg, #334155 0%, #1e293b 100%) !important;
  box-shadow:
    0 12px 40px rgba(15, 23, 42, 0.8),
    0 8px 24px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(148, 163, 184, 0.3) !important;
  border-color: rgba(148, 163, 184, 0.4) !important;
  color: #f1f5f9 !important;
}

/* 光效动画 */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

/* 脉冲动画（可选，增加注意力） */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow:
      0 8px 32px rgba(59, 130, 246, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
  50% {
    box-shadow:
      0 8px 32px rgba(59, 130, 246, 0.5),
      0 4px 16px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }
}

/* 可选：添加轻微的脉冲效果 */
#top-scroller a {
  animation: pulse-glow 3s ease-in-out infinite;
}

#top-scroller a:hover {
  animation: none;
}

/* 响应式优化 */
@media (max-width: 768px) {
  #top-scroller {
    width: 50px !important;
    height: 50px !important;
    bottom: 1.5rem !important;
    right: 1.5rem !important;
  }

  #top-scroller a {
    font-size: 1.25rem !important;
  }
}

@media (max-width: 480px) {
  #top-scroller {
    width: 45px !important;
    height: 45px !important;
    bottom: 1rem !important;
    right: 1rem !important;
  }

  #top-scroller a {
    font-size: 1.125rem !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  #top-scroller a {
    border-width: 3px !important;
    border-color: #000000 !important;
  }

  .dark #top-scroller a {
    border-color: #ffffff !important;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  #top-scroller a {
    transition: none !important;
    animation: none !important;
  }

  #top-scroller a::before {
    display: none !important;
  }

  #top-scroller a:hover {
    transform: none !important;
  }
}

/* ===== 文章正文自适应优雅阅读体验优化 ===== */

/* 字体加载优化 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* 全局字体变量定义 */
:root {
  --font-sans:
    "Inter",
    "SF Pro Text",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    "Roboto",
    "Helvetica Neue",
    "PingFang SC",
    "Hiragino Sans GB",
    "Noto Sans CJK SC",
    "Source Han Sans SC",
    "Microsoft YaHei UI",
    "Microsoft YaHei",
    sans-serif;

  --font-mono:
    "JetBrains Mono",
    "SF Mono",
    "Cascadia Code",
    "Fira Code",
    "Monaco",
    "Menlo",
    "Roboto Mono",
    "Source Code Pro",
    "Consolas",
    monospace;

  --font-display:
    "Inter",
    "SF Pro Display",
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    sans-serif;
}

/* 基础文章容器优化 */
.prose {
  max-width: none !important;
  width: 100%;
  margin: 0 auto;
  padding: 0;
  font-family: var(--font-sans);
  color: #374151;
  line-height: 1.75;
  font-size: 1.0625rem;
  font-weight: 400;
  letter-spacing: -0.005em;
  word-spacing: 0.02em;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
}

/* 深色模式文章样式 */
.dark .prose {
  color: #f3f4f6;  /* 提高对比度 */
}

/* 暗黑模式字体渲染优化 */
.dark .prose * {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* 暗黑模式下的字体权重调整 */
.dark .prose {
  font-weight: 400;
}

/* 暗黑模式下的文本阴影优化（提高清晰度） */
.dark .prose h1,
.dark .prose h2,
.dark .prose h3 {
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 暗黑模式下的字体对比度增强 */
@media (prefers-color-scheme: dark) {
  .dark .prose {
    font-weight: 400;
  }

  .dark .prose p,
  .dark .prose li {
    color: #e5e7eb;
    font-weight: 400;
  }

  .dark .prose strong {
    color: #ffffff;
    font-weight: 600;
  }

  .dark .prose em {
    color: #d1d5db;
    font-style: italic;
  }
}

/* 字体渲染优化 */
.prose * {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 强调文本优化 */
.prose strong {
  font-weight: 600;
  color: #1f2937;
}

.dark .prose strong {
  color: #f3f4f6;
}

.prose em {
  font-style: italic;
  color: #4b5563;
}

.dark .prose em {
  color: #d1d5db;
}

/* 小号文本优化 */
.prose small {
  font-size: 0.875em;
  color: #6b7280;
  font-weight: 400;
}

.dark .prose small {
  color: #9ca3af;
}

/* 文章标题层级优化 */
.prose h1 {
  font-size: clamp(1.875rem, 4vw, 2.5rem);
  font-weight: 700;
  line-height: 1.25;
  margin: 0 0 2rem 0;
  color: #111827;
  letter-spacing: -0.025em;
  font-family: var(--font-display);
}

.prose h2 {
  font-size: clamp(1.5rem, 3.5vw, 2rem);
  font-weight: 650;
  line-height: 1.3;
  margin: 3rem 0 1.5rem 0;
  color: #1f2937;
  letter-spacing: -0.02em;
  font-family: inherit;
}

.prose h3 {
  font-size: clamp(1.25rem, 3vw, 1.625rem);
  font-weight: 600;
  line-height: 1.4;
  margin: 2.5rem 0 1.25rem 0;
  color: #374151;
  letter-spacing: -0.015em;
  font-family: inherit;
}

.prose h4 {
  font-size: clamp(1.125rem, 2.5vw, 1.375rem);
  font-weight: 600;
  line-height: 1.4;
  margin: 2rem 0 1rem 0;
  color: #4b5563;
  letter-spacing: -0.01em;
  font-family: inherit;
}

.prose h5,
.prose h6 {
  font-size: clamp(1rem, 2vw, 1.125rem);
  font-weight: 600;
  line-height: 1.5;
  margin: 1.5rem 0 0.75rem 0;
  color: #6b7280;
  letter-spacing: -0.005em;
  font-family: inherit;
}

/* 深色模式标题颜色 */
.dark .prose h1 {
  color: #ffffff;
  font-weight: 700;
}
.dark .prose h2 {
  color: #f9fafb;
  font-weight: 650;
}
.dark .prose h3 {
  color: #f3f4f6;
  font-weight: 600;
}
.dark .prose h4 {
  color: #e5e7eb;
  font-weight: 600;
}
.dark .prose h5,
.dark .prose h6 {
  color: #d1d5db;
  font-weight: 600;
}

/* 段落优化 */
.prose p {
  margin: 1.5rem 0;
  line-height: 1.75;
  font-size: clamp(1rem, 2vw, 1.0625rem);
  font-weight: 400;
  max-width: 75ch;
  text-align: justify;
  text-justify: inter-ideograph;
  color: #374151;
  letter-spacing: -0.003em;
}

/* 暗黑模式段落优化 */
.dark .prose p {
  color: #0545cf;  段落文本使用稍浅的颜色
  font-weight: 400;
}

/* 列表优化 */
.prose ul,
.prose ol {
  margin: 1.5rem 0;
  padding-left: 1.5rem;
  max-width: 70ch;
}

.prose li {
  margin: 0.5rem 0;
  line-height: 1.7;
  font-size: clamp(1rem, 2vw, 1.0625rem);
  color: #374151;
}

/* 暗黑模式列表优化 */
.dark .prose li {
  color: #e5e7eb;
}

/* 引用块优化 */
.prose blockquote {
  margin: 2rem 0;
  padding: 1.5rem 2rem;
  border-left: 4px solid #3b82f6;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: normal;  /* 移除斜体 */
  font-size: clamp(1rem, 2vw, 1.0625rem);
  font-weight: 400;
  line-height: 1.7;
  max-width: 70ch;
  color: #4b5563;
  position: relative;
}

/* 引用块引号装饰 */
.prose blockquote::before {
  content: '"';
  position: absolute;
  top: 0.5rem;
  left: 0.75rem;
  font-size: 2rem;
  color: #3b82f6;
  font-weight: 700;
  line-height: 1;
  opacity: 0.3;
}

.dark .prose blockquote {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: #60a5fa;
  color: #d1d5db;
}

.dark .prose blockquote::before {
  color: #60a5fa;
}

/* 表格优化 */
.prose table {
  width: 100%;
  max-width: 100%;
  margin: 2rem 0;
  border-collapse: collapse;
  font-size: clamp(0.875rem, 1.8vw, 1rem);
  line-height: 1.6;
  overflow-x: auto;
  display: block;
  white-space: nowrap;
}

.prose thead {
  background: rgba(59, 130, 246, 0.05);
}

.dark .prose thead {
  background: rgba(59, 130, 246, 0.1);
}

.prose th,
.prose td {
  padding: 0.75rem 1rem;
  border: 1px solid #e5e7eb;
  text-align: left;
}

.dark .prose th,
.dark .prose td {
  border-color: #4b5563;  /* 更明显的边框 */
}

.prose th {
  font-weight: 600;
  color: #374151;
}

.dark .prose th {
  color: #f3f4f6;  /* 更亮的表头文字 */
  font-weight: 600;
}

.dark .prose td {
  color: #e5e7eb;  /* 表格内容颜色 */
}

/* 图片优化 */
.prose img {
  max-width: 100%;
  height: auto;
  margin: 2rem auto;
  display: block;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 链接优化 */
.prose a {
  color: #3b82f6;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.prose a:hover {
  color: #1d4ed8;
  border-bottom-color: #3b82f6;
}

.dark .prose a {
  color: #7dd3fc;  /* 更亮的蓝色，提高对比度 */
  font-weight: 500;
}

.dark .prose a:hover {
  color: #bae6fd;  /* 悬停时更亮 */
  border-bottom-color: #7dd3fc;
}

/* 代码内联优化 */
.prose code:not(pre code) {
  background: rgba(59, 130, 246, 0.1);
  color: #1e40af;
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-size: 0.875em;
  font-weight: 500;
  font-family: var(--font-mono);
  letter-spacing: -0.02em;
}

.dark .prose code:not(pre code) {
  background: rgba(59, 130, 246, 0.15);
  color: #a5b4fc;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 分隔线优化 */
.prose hr {
  margin: 3rem 0;
  border: none;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e5e7eb, transparent);
}

.dark .prose hr {
  background: linear-gradient(90deg, transparent, #374151, transparent);
}

/* 响应式布局优化 */

/* 超大屏幕 (≥1536px) */
@media (min-width: 1536px) {
  .prose {
    max-width: 85ch;
    margin: 0 auto;
    padding: 0 3rem;
    font-size: 1.125rem;
  }

  .prose p {
    max-width: 80ch;
    font-size: 1.125rem;
  }

  .prose ul,
  .prose ol {
    max-width: 75ch;
  }

  .prose blockquote {
    max-width: 75ch;
    font-size: 1.125rem;
  }

  .prose table {
    font-size: 1rem;
  }
}

/* 大屏幕 (1280px-1535px) */
@media (min-width: 1280px) and (max-width: 1535px) {
  .prose {
    max-width: 80ch;
    margin: 0 auto;
    padding: 0 2.5rem;
    font-size: 1.0625rem;
  }

  .prose p {
    max-width: 75ch;
  }

  .prose ul,
  .prose ol {
    max-width: 70ch;
  }

  .prose blockquote {
    max-width: 70ch;
  }
}

/* 中等屏幕 (1024px-1279px) */
@media (min-width: 1024px) and (max-width: 1279px) {
  .prose {
    max-width: 75ch;
    margin: 0 auto;
    padding: 0 2rem;
    font-size: 1.0625rem;
  }

  .prose p {
    max-width: 70ch;
  }

  .prose ul,
  .prose ol {
    max-width: 65ch;
  }

  .prose blockquote {
    max-width: 65ch;
  }
}

/* 平板设备 (768px-1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  .prose {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 1.5rem;
    font-size: 1rem;
  }

  .prose p {
    max-width: 100%;
    text-align: left;
  }

  .prose ul,
  .prose ol {
    max-width: 100%;
  }

  .prose blockquote {
    max-width: 100%;
    padding: 1rem 1.5rem;
  }

  .prose table {
    font-size: 0.875rem;
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .prose th,
  .prose td {
    padding: 0.5rem 0.75rem;
  }
}

/* 移动设备 (≤767px) */
@media (max-width: 767px) {
  .prose {
    max-width: 100%;
    margin: 0;
    padding: 0 1rem;
    font-size: 1rem;
    line-height: 1.7;
  }

  .prose h1 {
    font-size: clamp(1.5rem, 6vw, 2rem);
    margin: 0 0 1.5rem 0;
  }

  .prose h2 {
    font-size: clamp(1.25rem, 5vw, 1.625rem);
    margin: 2rem 0 1rem 0;
  }

  .prose h3 {
    font-size: clamp(1.125rem, 4vw, 1.375rem);
    margin: 1.5rem 0 0.75rem 0;
  }

  .prose h4,
  .prose h5,
  .prose h6 {
    font-size: clamp(1rem, 3vw, 1.125rem);
    margin: 1.25rem 0 0.5rem 0;
  }

  .prose p {
    margin: 1rem 0;
    max-width: 100%;
    text-align: left;
    font-size: 1rem;
    line-height: 1.7;
  }

  .prose ul,
  .prose ol {
    margin: 1rem 0;
    padding-left: 1.25rem;
    max-width: 100%;
  }

  .prose li {
    margin: 0.25rem 0;
    font-size: 1rem;
  }

  .prose blockquote {
    margin: 1.5rem 0;
    padding: 1rem;
    max-width: 100%;
    font-size: 1rem;
  }

  .prose table {
    font-size: 0.8125rem;
    margin: 1.5rem 0;
  }

  .prose th,
  .prose td {
    padding: 0.5rem;
    font-size: 0.8125rem;
  }

  .prose img {
    margin: 1.5rem auto;
  }
}

/* 小屏幕移动设备 (≤480px) */
@media (max-width: 480px) {
  .prose {
    padding: 0 0.75rem;
    font-size: 0.9375rem;
  }

  .prose h1 {
    font-size: clamp(1.375rem, 7vw, 1.75rem);
  }

  .prose h2 {
    font-size: clamp(1.125rem, 6vw, 1.5rem);
  }

  .prose h3 {
    font-size: clamp(1rem, 5vw, 1.25rem);
  }

  .prose p {
    font-size: 0.9375rem;
    line-height: 1.6;
  }

  .prose li {
    font-size: 0.9375rem;
  }

  .prose blockquote {
    padding: 0.75rem;
    font-size: 0.9375rem;
  }

  .prose table {
    font-size: 0.75rem;
  }

  .prose th,
  .prose td {
    padding: 0.375rem;
    font-size: 0.75rem;
  }
}

/* 阅读体验增强 */

/* 选中文本样式 */
.prose ::selection {
  background: rgba(59, 130, 246, 0.2);
  color: inherit;
}

.dark .prose ::selection {
  background: rgba(59, 130, 246, 0.3);
}

/* 焦点样式优化 */
.prose a:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 2px;
}

/* 打印样式优化 */
@media print {
  .prose {
    max-width: none;
    font-size: 12pt;
    line-height: 1.5;
    color: #000;
  }

  .prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6 {
    color: #000;
    page-break-after: avoid;
  }

  .prose p,
  .prose li {
    orphans: 3;
    widows: 3;
  }

  .prose blockquote {
    page-break-inside: avoid;
  }

  .prose table {
    page-break-inside: avoid;
  }

  .prose img {
    max-width: 100%;
    page-break-inside: avoid;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .prose {
    color: #000;
  }

  .dark .prose {
    color: #fff;
  }

  .prose a {
    color: #0000ee;
    text-decoration: underline;
  }

  .dark .prose a {
    color: #add8e6;
  }

  .prose code:not(pre code) {
    background: #f0f0f0;
    color: #000;
    border: 1px solid #ccc;
  }

  .dark .prose code:not(pre code) {
    background: #333;
    color: #fff;
    border: 1px solid #666;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .prose * {
    transition: none !important;
    animation: none !important;
  }
}

/* 文章内容容器优化 */
.article-content {
  width: 100%;
  max-width: 100%;
}

/* 确保文章在不同布局下的正确显示 */
article .prose {
  width: 100%;
  max-width: 100%;
}

/* ===== 文章布局优化 ===== */

/* 基础文章布局 */
.prose {
  width: 100% !important;
  max-width: none !important;
}

/* ===== 有TOC时的文章布局优化  */
section.prose.lg\:flex-row {
  display: flex !important;
  flex-direction: row !important;
  gap: 2rem !important;
  /* align-items: flex-start !important; */
}

/* 文章主内容区域 */
section.prose.lg\:flex-row > div:first-child {
  flex: 1 !important;
  min-width: 0 !important;
  max-width: none !important;
}

/* TOC侧边栏区域 */
section.prose.lg\:flex-row > div:last-child {
  flex-shrink: 0 !important;
  width: 280px !important;
  margin-left: 2rem !important;
}

/* 大屏幕优化 (≥1280px) */
@media (min-width: 1280px) {
  section.prose.lg\:flex-row {
    gap: 3rem !important;
  }

  section.prose.lg\:flex-row > div:last-child {
    width: 300px !important;
    margin-left: 3rem !important;
  }

  /* 确保正文有足够宽度 */
  section.prose.lg\:flex-row > div:first-child {
    max-width: calc(100vw - 400px) !important;
  }
}

/* 中等屏幕优化 (1024px-1279px) */
@media (min-width: 1024px) and (max-width: 1279px) {
  section.prose.lg\:flex-row {
    gap: 1.5rem !important;
  }

  section.prose.lg\:flex-row > div:last-child {
    width: 240px !important;
    margin-left: 1.5rem !important;
  }

  /* 确保正文有足够宽度 */
  section.prose.lg\:flex-row > div:first-child {
    max-width: calc(100vw - 320px) !important;
  }
}

/* 小屏幕时恢复单列布局 */
@media (max-width: 1023px) {
  section.prose.lg\:flex-row {
    flex-direction: column !important;
    gap: 1rem !important;
  }

  section.prose.lg\:flex-row > div:first-child,
  section.prose.lg\:flex-row > div:last-child {
    width: 100% !important;
    max-width: 100% !important;
    margin-left: 0 !important;
  }
}

/* 文章内容区域优化 */
.prose .min-w-0 {
  min-width: 0 !important;
  width: 100% !important;
  overflow-wrap: break-word !important;
}

/* 确保代码块不会溢出 */
.prose pre {
  max-width: 100% !important;
  overflow-x: auto !important;
}

/* 确保表格不会溢出 */
.prose table {
  max-width: 100% !important;
  overflow-x: auto !important;
  display: block !important;
  white-space: nowrap !important;
}

/* 确保图片不会溢出 */
.prose img {
  max-width: 100% !important;
  height: auto !important;
}

/* ===== 文章列表页面优化 ===== */

/* 年份标题优化 */
h2.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300,
h2.mt-12.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300 {
  font-size: 2.5rem !important;  /* 从 text-2xl (1.5rem) 增大到 2.5rem */
  font-weight: 800 !important;
  margin-top: 3rem !important;
  margin-bottom: 1.5rem !important;
  color: #1f2937 !important;
  font-family: var(--font-display);
  letter-spacing: -0.02em;
  position: relative;
}

/* 暗黑模式年份标题 */
.dark h2.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300,
.dark h2.mt-12.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300 {
  color: #f9fafb !important;
}

/* 年份标题装饰线 */
h2.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300::after,
h2.mt-12.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 3rem;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 2px;
}

/* 文章列表项紧凑化 */
.article {
  margin-bottom: 1rem !important;  /* 减少文章间距 */
  padding: 1rem !important;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.article:hover {
  background: rgba(59, 130, 246, 0.02);
  border-color: rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05);
}

.dark .article:hover {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

/* 文章标题优化 */
.article .font-bold.text-xl {
  font-size: 1.375rem !important;  /* 稍微增大标题 */
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin-bottom: 0.5rem !important;
  font-family: var(--font-display);
  letter-spacing: -0.01em;
}

/* 文章元信息紧凑化 */
.article .text-sm.text-neutral-500 {
  margin-top: 0.5rem !important;
  line-height: 1.4 !important;
}

/* 标签间距优化 */
.article .flex.flex-row.flex-wrap.items-center span {
  margin-top: 0.25rem !important;
  margin-right: 0.5rem !important;
}

/* 文章列表容器优化 */
section.space-y-10 {
  gap: 1.5rem !important;  /* 减少整体间距 */
}

/* 响应式优化 */
@media (max-width: 768px) {
  h2.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300,
  h2.mt-12.text-2xl.font-bold.text-neutral-700.first\:mt-8.dark\:text-neutral-300 {
    font-size: 2rem !important;
    margin-top: 2rem !important;
    margin-bottom: 1rem !important;
  }

  .article {
    margin-bottom: 0.75rem !important;
    padding: 0.75rem !important;
  }

  .article .font-bold.text-xl {
    font-size: 1.25rem !important;
  }
}

/* TOC 容器优化 */
.toc-right {
  max-width: 280px !important;
  min-width: 240px !important;
  width: 280px !important;
  position: sticky !important;
  top: 1rem !important;
  align-self: flex-start !important;
  height: fit-content !important;
  max-height: calc(100vh - 2rem) !important;
  overflow-y: auto !important;
}

.toc {
  z-index: 10 !important;
}
/* 确保TOC在正确的层级 */
.order-first.lg\\:ml-auto.px-0.lg\\:order-last.ltr\\:lg\\:pl-8.rtl\\:lg\\:pr-8 {
  position: relative !important;
  z-index: 10 !important;
}

/* TOC 内容区域优化 */
.toc-right .min-w-\[220px\] {
  min-width: 240px !important;
  max-width: 280px !important;
  padding: 1rem !important;
}

/* ===== 修复TOC粘性定位  */
.toc .lg\\:sticky,
.toc-right.lg\\:sticky,
details#TOCView.toc-right {
  position: sticky !important;
  top: 1rem !important;
  height: fit-content !important;
  max-height: calc(100vh - 2rem) !important;
  overflow-y: auto !important;
  z-index: 10 !important;
}

/* 确保TOC父容器支持粘性定位 */
.toc,
.order-first.lg\\:ml-auto.px-0.lg\\:order-last {
  position: relative !important;
  height: fit-content !important;
}

/* 强制覆盖主题的TOC定位设置 */
@media (min-width: 1024px) {
  .lg\:top-10,
  .lg\:top-\[140px\] {
    top: 1rem !important;
  }
}

/* ===== TOC 导航链接基础样式  */
#TableOfContents {
  font-size: 0.875rem !important;
  line-height: 1.4 !important;
}

#TableOfContents ul {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}

#TableOfContents li {
  margin: 0 !important;
  padding: 0 !important;
  position: relative;
}

/* ===== TOC 链接样式优化  */
#TableOfContents a {
  display: block !important;
  padding: 0.375rem 0.5rem !important;
  margin: 0.125rem 0 !important;
  color: #6b7280 !important;
  text-decoration: none !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  font-weight: 400 !important;
  line-height: 1.4 !important;

  /* 文字换行和溢出处理 */
  word-wrap: break-word !important;
  word-break: break-word !important;
  hyphens: auto !important;
  overflow-wrap: break-word !important;
  white-space: normal !important;
  max-width: 100% !important;
}

/* ===== TOC 链接交互效果  */
#TableOfContents a:hover {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #3b82f6 !important;
  transform: translateX(2px) !important;
}

/* TOC 链接激活状态 */
#TableOfContents a.active,
#TableOfContents a:target {
  background: rgba(59, 130, 246, 0.15) !important;
  color: #1d4ed8 !important;
  font-weight: 500 !important;
  border-left: 3px solid #3b82f6 !important;
  padding-left: 0.375rem !important;
}

/* TOC 层级缩进优化 */
#TableOfContents ul ul {
  margin-left: 0.75rem !important;
  border-left: 1px solid #e5e7eb !important;
  padding-left: 0.5rem !important;
}

#TableOfContents ul ul ul {
  margin-left: 0.75rem !important;
  border-left: 1px solid #f3f4f6 !important;
}

/* 不同层级的字体大小 */
#TableOfContents > ul > li > a {
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  color: #374151 !important;
}

#TableOfContents > ul > li > ul > li > a {
  font-size: 0.8125rem !important;
  font-weight: 400 !important;
  color: #6b7280 !important;
  padding-left: 0.75rem !important;
}

#TableOfContents > ul > li > ul > li > ul > li > a {
  font-size: 0.75rem !important;
  font-weight: 400 !important;
  color: #9ca3af !important;
  padding-left: 1rem !important;
}

/* 暗黑模式 TOC 优化 */
.dark #TableOfContents a {
  color: #9ca3af !important;
}

.dark #TableOfContents a:hover {
  background: rgba(59, 130, 246, 0.2) !important;
  color: #60a5fa !important;
}

.dark #TableOfContents a.active,
.dark #TableOfContents a:target {
  background: rgba(59, 130, 246, 0.25) !important;
  color: #93c5fd !important;
  border-left-color: #60a5fa !important;
}

.dark #TableOfContents > ul > li > a {
  color: #e5e7eb !important;
}

.dark #TableOfContents > ul > li > ul > li > a {
  color: #d1d5db !important;
}

.dark #TableOfContents > ul > li > ul > li > ul > li > a {
  color: #9ca3af !important;
}

.dark #TableOfContents ul ul {
  border-left-color: #4b5563 !important;
}

.dark #TableOfContents ul ul ul {
  border-left-color: #374151 !important;
}

/* ===== TOC 滚动条优化  */
.toc-right {
  scrollbar-width: thin !important;
  scrollbar-color: #d1d5db #f9fafb !important;
}

.toc-right::-webkit-scrollbar {
  width: 6px !important;
}

.toc-right::-webkit-scrollbar-track {
  background: #f9fafb !important;
  border-radius: 3px !important;
}

.toc-right::-webkit-scrollbar-thumb {
  background: #d1d5db !important;
  border-radius: 3px !important;
}

.toc-right::-webkit-scrollbar-thumb:hover {
  background: #9ca3af !important;
}

.dark .toc-right {
  scrollbar-color: #4b5563 #1f2937 !important;
}

.dark .toc-right::-webkit-scrollbar-track {
  background: #1f2937 !important;
}

.dark .toc-right::-webkit-scrollbar-thumb {
  background: #4b5563 !important;
}

.dark .toc-right::-webkit-scrollbar-thumb:hover {
  background: #6b7280 !important;
}

/* TOC 标题优化 */
.toc-right summary {
  font-size: 1rem !important;
  font-weight: 600 !important;
  padding: 0.75rem 1rem !important;
  margin-bottom: 0.5rem !important;
  border-radius: 6px !important;
  background: #f8fafc !important;
  color: #374151 !important;
  border: 1px solid #e2e8f0 !important;
}

.dark .toc-right summary {
  background: #1e293b !important;
  color: #e2e8f0 !important;
  border-color: #334155 !important;
}

/* 长文本处理优化 */
#TableOfContents a {
  /* 确保长文本能够正确换行 */
  display: block !important;
  width: 100% !important;
  box-sizing: border-box !important;
}

/* 特殊字符和数字处理 */
#TableOfContents a[href*="#"] {
  /* 对于包含特殊字符的链接进行额外处理 */
  word-break: break-all !important;
}

/* 中文文本优化 */
#TableOfContents a {
  /* 中文文本换行优化 */
  word-break: keep-all !important;
  overflow-wrap: anywhere !important;
  line-break: auto !important;
}

/* 英文长单词处理 */
#TableOfContents a[href*="configuration"],
#TableOfContents a[href*="installation"],
#TableOfContents a[href*="troubleshooting"] {
  word-break: break-word !important;
  hyphens: auto !important;
}

/* TOC 容器边框优化 */
.toc-right .border-dotted {
  border-color: #e5e7eb !important;
  border-style: solid !important;
  border-width: 1px !important;
  border-radius: 8px !important;
  background: rgba(248, 250, 252, 0.5) !important;
}

.dark .toc-right .border-dotted {
  border-color: #374151 !important;
  background: rgba(30, 41, 59, 0.5) !important;
}

/* TOC 响应式优化 */
@media (min-width: 1280px) {
  .toc-right {
    max-width: 300px !important;
    min-width: 280px !important;
    width: 300px !important;
  }

  .toc-right .min-w-\[220px\] {
    min-width: 280px !important;
    max-width: 300px !important;
    padding: 1.25rem !important;
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .toc-right {
    max-width: 240px !important;
    min-width: 220px !important;
    width: 240px !important;
  }

  .toc-right .min-w-\[220px\] {
    min-width: 220px !important;
    max-width: 240px !important;
    padding: 0.875rem !important;
  }

  #TableOfContents {
    font-size: 0.8125rem !important;
  }

  #TableOfContents > ul > li > a {
    font-size: 0.8125rem !important;
  }

  #TableOfContents > ul > li > ul > li > a {
    font-size: 0.75rem !important;
  }

  #TableOfContents > ul > li > ul > li > ul > li > a {
    font-size: 0.6875rem !important;
  }
}

@media (max-width: 1023px) {
  /* 在较小屏幕上隐藏右侧TOC，使用移动端TOC */
  .toc-right {
    display: none !important;
  }
}

/* ===== 文章分页导航优化 ===== */

/* 分页导航容器 - 优雅简洁的网格布局 */
nav[aria-label="Article navigation"] {
  max-width: 100% !important;
}

/* 网格布局优化 - 确保左右平衡 */
nav[aria-label="Article navigation"] .grid {
  align-items: stretch !important;
}

/* 分页链接基础样式 - 统一高度的卡片式设计 */
nav[aria-label="Article navigation"] a {
  display: flex !important;
  text-decoration: none !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease-in-out !important;
  background: transparent !important;
  min-height: 100px !important;
}

/* 分页链接悬停效果 - 优雅的边框和颜色变化 */
nav[aria-label="Article navigation"] a:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.dark nav[aria-label="Article navigation"] a:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* 方向标签样式优化 */
nav[aria-label="Article navigation"] .uppercase {
  font-size: 0.75rem !important;
  letter-spacing: 0.05em !important;
  font-weight: 500 !important;
  opacity: 0.8 !important;
}

/* 标题文本处理 - 优雅的长标题处理 */
nav[aria-label="Article navigation"] h3 {
  font-family: inherit !important;
  font-size: 0.95rem !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
  letter-spacing: normal !important;

  /* 智能文本截断 */
  word-wrap: break-word !important;
  word-break: keep-all !important;
  overflow-wrap: anywhere !important;
  hyphens: auto !important;

  /* 多行文本处理 */
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  max-height: calc(1.4em * 2) !important;
}

/* SVG 图标样式 */
nav[aria-label="Article navigation"] svg {
  transition: transform 0.2s ease-in-out !important;
}

nav[aria-label="Article navigation"] a:hover svg {
  transform: scale(1.1) !important;
}

/* 内容区域宽度控制 */
nav[aria-label="Article navigation"] .min-w-0 {
  min-width: 0 !important;
  flex: 1 !important;
  overflow: hidden !important;
}

/* 确保卡片等高 */
nav[aria-label="Article navigation"] .order-1,
nav[aria-label="Article navigation"] .order-2 {
  display: flex !important;
  flex-direction: column !important;
}

nav[aria-label="Article navigation"] .order-1 a,
nav[aria-label="Article navigation"] .order-2 a {
  flex: 1 !important;
}

/* 响应式优化 */
@media (max-width: 768px) {
  nav[aria-label="Article navigation"] {
    padding-top: 1.5rem !important;
  }

  nav[aria-label="Article navigation"] .grid {
    grid-template-columns: 1fr !important;
    gap: 1rem !important;
  }

  nav[aria-label="Article navigation"] .order-1 {
    order: 2 !important;
  }

  nav[aria-label="Article navigation"] .order-2 {
    order: 1 !important;
  }

  nav[aria-label="Article navigation"] .text-right {
    text-align: left !important;
  }

  nav[aria-label="Article navigation"] h3 {
    font-size: 0.9rem !important;
    -webkit-line-clamp: 2 !important;
    line-clamp: 2 !important;
    max-height: calc(1.3em * 2) !important;
  }

  nav[aria-label="Article navigation"] a {
    min-height: 80px !important;
  }
}

/* 英文长单词特殊处理 */
nav[aria-label="Article navigation"] h3[title*="configuration"],
nav[aria-label="Article navigation"] h3[title*="installation"],
nav[aria-label="Article navigation"] h3[title*="troubleshooting"],
nav[aria-label="Article navigation"] h3[title*="implementation"] {
  word-break: break-word !important;
}

/* 无障碍优化 */
nav[aria-label="Article navigation"] a:focus {
  outline: 2px solid #3b82f6 !important;
  outline-offset: 2px !important;
  border-radius: 0.5rem !important;
}

.dark nav[aria-label="Article navigation"] a:focus {
  outline-color: #60a5fa !important;
}

/* 打印样式优化 */
@media print {
  nav[aria-label="Article navigation"] {
    display: none !important;
  }
}

/* 分隔线样式优化 */
nav[aria-label="Article navigation"] hr {
  opacity: 0.6 !important;
  border-style: dotted !important;
}

/* 单个导航项的居中布局优化 */
nav[aria-label="Article navigation"] .justify-center {
  display: flex !important;
  justify-content: center !important;
}

nav[aria-label="Article navigation"] .max-w-md {
  max-width: 28rem !important;
  width: 100% !important;
}

/* 单个导航项的特殊样式 */
nav[aria-label="Article navigation"] .justify-center a {
  min-height: 100px !important;
  width: 100% !important;
}

/* 单个下一篇文章的居中对齐 */
nav[aria-label="Article navigation"] .text-center {
  text-align: center !important;
}

/* 响应式优化 - 移动端单项布局 */
@media (max-width: 768px) {
  nav[aria-label="Article navigation"] .max-w-md {
    max-width: 100% !important;
  }

  nav[aria-label="Article navigation"] .justify-center a {
    min-height: 80px !important;
  }

  nav[aria-label="Article navigation"] .text-center {
    text-align: left !important;
  }
}

/* Mermaid 图表样式优化 */
.mermaid {
  /* 基础样式 */
  margin: 1rem 0;
  text-align: center;
  background-color: transparent;

  /* 确保图表在容器中居中 */
  display: flex;
  justify-content: center;
  align-items: center;

  /* 响应式设计 */
  max-width: 100%;
  overflow-x: auto;
}

/* Mermaid SVG 容器样式 */
.mermaid svg {
  max-width: 100%;
  height: auto;
  background-color: transparent !important;
}

/* 明亮主题下的文本颜色修正 */
.mermaid .nodeLabel,
.mermaid .edgeLabel,
.mermaid .cluster-label,
.mermaid .titleText,
.mermaid text {
  fill: var(--color-neutral-700) !important;
}

/* 暗黑主题下的文本颜色修正 */
[data-theme="dark"] .mermaid .nodeLabel,
[data-theme="dark"] .mermaid .edgeLabel,
[data-theme="dark"] .mermaid .cluster-label,
[data-theme="dark"] .mermaid .titleText,
[data-theme="dark"] .mermaid text,
.dark .mermaid .nodeLabel,
.dark .mermaid .edgeLabel,
.dark .mermaid .cluster-label,
.dark .mermaid .titleText,
.dark .mermaid text {
  fill: var(--color-neutral-200) !important;
}

/* 节点边框和背景 */
.mermaid .node rect,
.mermaid .node circle,
.mermaid .node ellipse,
.mermaid .node polygon {
  stroke-width: 2px;
  stroke: var(--color-neutral-400);
  fill: var(--color-neutral-100);
}

/* 暗黑主题下的节点样式 */
[data-theme="dark"] .mermaid .node rect,
[data-theme="dark"] .mermaid .node circle,
[data-theme="dark"] .mermaid .node ellipse,
[data-theme="dark"] .mermaid .node polygon,
.dark .mermaid .node rect,
.dark .mermaid .node circle,
.dark .mermaid .node ellipse,
.dark .mermaid .node polygon {
  stroke: var(--color-neutral-500);
  fill: var(--color-neutral-700);
}

/* 连接线样式 */
.mermaid .edgePath .path {
  stroke: var(--color-neutral-500);
  stroke-width: 2px;
  fill: none;
}

/* 暗黑主题下的连接线 */
[data-theme="dark"] .mermaid .edgePath .path,
.dark .mermaid .edgePath .path {
  stroke: var(--color-neutral-400);
}

/* 边缘标签样式 */
.mermaid .edgeLabel {
  background-color: var(--color-neutral-50);
  color: var(--color-neutral-700);
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
}

/* 暗黑主题下的边缘标签 */
[data-theme="dark"] .mermaid .edgeLabel,
.dark .mermaid .edgeLabel {
  background-color: var(--color-neutral-800);
  color: var(--color-neutral-200);
}

/* 箭头样式 */
.mermaid .arrowheadPath {
  fill: var(--color-neutral-500);
  stroke: var(--color-neutral-500);
}

/* 暗黑主题下的箭头 */
[data-theme="dark"] .mermaid .arrowheadPath,
.dark .mermaid .arrowheadPath {
  fill: var(--color-neutral-400);
  stroke: var(--color-neutral-400);
}

/* 集群/子图样式 */
.mermaid .cluster rect {
  fill: var(--color-neutral-50);
  stroke: var(--color-neutral-300);
  stroke-width: 1px;
  stroke-dasharray: 5,5;
}

/* 暗黑主题下的集群样式 */
[data-theme="dark"] .mermaid .cluster rect,
.dark .mermaid .cluster rect {
  fill: var(--color-neutral-800);
  stroke: var(--color-neutral-600);
}

/* 特殊节点类型的颜色 */
.mermaid .node.primary rect,
.mermaid .node.primary circle,
.mermaid .node.primary ellipse,
.mermaid .node.primary polygon {
  fill: var(--color-primary-200);
  stroke: var(--color-primary-400);
}

/* 暗黑主题下的特殊节点 */
[data-theme="dark"] .mermaid .node.primary rect,
[data-theme="dark"] .mermaid .node.primary circle,
[data-theme="dark"] .mermaid .node.primary ellipse,
[data-theme="dark"] .mermaid .node.primary polygon,
.dark .mermaid .node.primary rect,
.dark .mermaid .node.primary circle,
.dark .mermaid .node.primary ellipse,
.dark .mermaid .node.primary polygon {
  fill: var(--color-primary-700);
  stroke: var(--color-primary-500);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .mermaid {
    font-size: 14px;
  }

  .mermaid svg {
    max-width: 100%;
    overflow-x: auto;
  }
}

/* 确保文字可读性 */
.mermaid text {
  fill: currentColor !important;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

/* 节点特定样式 */
.mermaid .rootNode rect {
  fill: var(--mermaid-primary-color) !important;
  stroke: var(--mermaid-primary-border) !important;
}

.mermaid .rootNode text {
  fill: var(--mermaid-primary-text) !important;
  font-weight: bold;
  font-size: 14px;
}

.mermaid .categoryNode rect {
  fill: var(--mermaid-secondary-color) !important;
  stroke: var(--mermaid-secondary-border) !important;
}

.mermaid .categoryNode text {
  fill: var(--mermaid-secondary-text) !important;
  font-weight: bold;
  font-size: 12px;
}

.mermaid .featureNode rect {
  fill: var(--mermaid-tertiary-color) !important;
  stroke: var(--mermaid-tertiary-border) !important;
}

.mermaid .featureNode text {
  fill: var(--mermaid-tertiary-text) !important;
  font-weight: normal;
  font-size: 11px;
}