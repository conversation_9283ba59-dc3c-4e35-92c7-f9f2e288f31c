---
title: "Nexus3 PyPI 私服 Blob Storage 异常修复记录"
date: 2021-07-22T15:38:00+08:00
draft: false
tags: ["nexus3","fix","pypi","jenkins","pipeline"]
tags_weight: 20
categories: ["devops"]
categories_weight: 20
keywords:
- nexus3
- devops
- fix
- pypi
- 私服
- 下载失败
- error
- blob
- orientdb
- pipeline
- jenkins
description: "DevOps Jenkins Pipeline 使用 Nexus3 PyPI 私服时 Blob Storage 异常的修复过程记录。"
---

# 问题描述

在 DevOps 集成环境的测试环境中，发现多条 Pipeline 持续处于构建超时状态。初步排查以为是依赖版本兼容性问题导致 pip 无法找到合适版本，与开发团队联合排查后发现：开发环境正常，测试环境异常，且使用相同的代码和 Dockerfile 文件，排除了版本相关问题。具体错误表现如下：

![image-20210721170700491](https://cdn.treesir.pub/img/image-20210721170700491.png)

**检查 Nexus3 私服日志**

```bash
docker logs -f --tail 100 nexus3
```

![image-20210721171037634](https://cdn.treesir.pub/img/image-20210721171037634.png)

![image-20210721171052792](https://cdn.treesir.pub/img/image-20210721171052792.png)

检查对应 blob 中的文件内容发现为空，结合日志分析，初步判断是 blob storage 对应的存储数据丢失导致。

# 修复过程

## 方法一：重建数据库索引

尝试对数据库进行重建索引修复。Nexus3 内部使用 OrientDB 数据库，相关[文档](https://orientdb.com/docs/last/dotnet/NET-Transactions-Delete.html)可供参考。

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# 用户名/密码: admin admin（默认密码）
CONNECT PLOCAL:/nexus-data/db/component/ admin admin
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT
```

![image-20210721172248837](https://cdn.treesir.pub/img/image-20210721172248837.png)

重新触发 Pipeline 测试，问题依然存在，此方法无效。

![image-20210721172720110](https://cdn.treesir.pub/img/image-20210721172720110.png)

## 方法二：备份恢复数据

备份 Nexus3 数据，删除旧数据，然后基于备份数据进行恢复。

**创建备份数据任务**

![image-20210721172844252](https://cdn.treesir.pub/img/image-20210721172844252.png)

**选择备份导出数据库**

![image-20210721172857718](https://cdn.treesir.pub/img/image-20210721172857718.png)

**设置备份任务**

![image-20210721173128605](https://cdn.treesir.pub/img/image-20210721173128605.png)

**启动备份任务**

![image-20210721173158292](https://cdn.treesir.pub/img/image-20210721173158292.png)

**等待任务执行成功**

![image-20210722160039770](https://cdn.treesir.pub/img/image-20210722160039770.png)

由于导出过程中被中断，数据无法导出，此方法暂时放弃。

## 方法三：Blob Storage 元数据修复

创建任务对 Blob Storage 存储进行元数据修复。



**创建修复 Blob 元数据任务**

![image-20210722160512127](https://cdn.treesir.pub/img/image-20210722160512127.png)

**选择需要修复的 Blob Storage**

![image-20210722160931972](https://cdn.treesir.pub/img/image-20210722160931972.png)

等待任务执行完毕。

**再次执行数据重建索引任务**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# 用户名/密码: admin admin（默认密码）
CONNECT PLOCAL:/nexus-data/db/component/ admin admin
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT
```

**重建索引后出现新问题**

部分包显示 `404` 错误。

![image-20210722100816785](https://cdn.treesir.pub/img/image-20210722100816785.png)

![image-20210722100943816](https://cdn.treesir.pub/img/image-20210722100943816.png)

![image-20210722100716967](https://cdn.treesir.pub/img/image-20210722100716967.png)

## 修复依赖拉取 404 问题

创建重建私服浏览和搜索功能的任务。

**创建相关任务**

![image-20210722162439277](https://cdn.treesir.pub/img/image-20210722162439277.png)

**选择对所有仓库生效**

![image-20210722101653481](https://cdn.treesir.pub/img/image-20210722101653481.png)

![image-20210722162501026](https://cdn.treesir.pub/img/image-20210722162501026.png)

**启动重建任务**

![image-20210722101719109](https://cdn.treesir.pub/img/image-20210722101719109.png)

**等待任务结束**

![image-20210722101954917](https://cdn.treesir.pub/img/image-20210722101954917.png)

![image-20210722162536184](https://cdn.treesir.pub/img/image-20210722162536184.png)



**再次启动 Pipeline 测试，仍然报错，开启 TRACE 级别日志查看详情**

> 注意：TRACE 日志级别建议仅在测试时开启，使用后及时关闭，日志内容过多。

![image-20210722162741547](https://cdn.treesir.pub/img/image-20210722162741547.png)

![image-20210722162807053](https://cdn.treesir.pub/img/image-20210722162807053.png)

![image-20210722122658097](https://cdn.treesir.pub/img/image-20210722122658097.png)

从日志中可以看到 `Could not dispatch event AssetCreatedEvent` 报错。在社区寻找解决方案，暂未找到合适的方法。

**继续尝试重建索引**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# 用户名/密码: admin admin（默认密码）
CONNECT PLOCAL:/nexus-data/db/component/ admin admin
REBUILD INDEX *
```

![image-20210722140438216](https://cdn.treesir.pub/img/image-20210722140438216.png)

执行重建索引时直接报错，提示发现重复的 key。



**执行删除操作，重启系统后将自动完成索引重建**

```bash
drop class browse_node
DISCONNECT

exit
```

**再次进行数据库重建索引及修复操作**

```bash
docker exec -it nexus3 bash

cd /nexus-data

java -jar /opt/sonatype/nexus/lib/support/nexus-orient-console.jar

# 用户名/密码: admin admin（默认密码）
CONNECT PLOCAL:/nexus-data/db/component/ admin admin
REBUILD INDEX *
REPAIR DATABASE --fix-graph
REPAIR DATABASE --fix-links
REPAIR DATABASE --fix-ridbags
REPAIR DATABASE --fix-bonsai
DISCONNECT

docker restart nexus3
```

![image-20210722142059307](https://cdn.treesir.pub/img/image-20210722142059307.png)

再次执行时，已不再出现重复 key 的报错。

**重启后查看系统日志**

![image-20210722142718148](https://cdn.treesir.pub/img/image-20210722142718148.png)

从日志中可以看到，Nexus 正在对存储库浏览树进行重建工作，此时已从日志中看出重建完成。



**观察后续日志输出无相关错误，尝试触发 Pipeline 测试**

![image-20210722152904777](https://cdn.treesir.pub/img/image-20210722152904777.png)

可以看到已能正常拉取 PyPI 包依赖。

# 参考链接

- https://community.sonatype.com/t/unable-to-reach-metadata-file-instead-get-http-404-nof-found-error/5357
- https://issues.sonatype.org/browse/NEXUS-21814?jql=text%20~%20%22AssetCreatedEvent%22
- https://orientdb.com/docs/last/index.html?q=delete

# 总结

经过此次事件，发现 Nexus3 的 bug 确实较多，建议生产环境使用社区反响较好、bug 较少的稳定版本。

此类问题我在一年前也遇到过，当时采用的解决方式是创建新的 Blob 存储，将有问题的私服进行迁移。这种方式相对简单，但如果有多个私服共用一个 Blob 存储时，数据需要重新上传，迁移过程会比较麻烦和繁琐。

**建议**：为不同环境和不同类型的私服设置独立的 Blob Storage，以分散风险。
