---
title: "使用 Docker 快速部署 Redis 服务器"
date: 2020-11-30T10:35:47+08:00
draft: false
tags: [ "redis","centos7","docker"]
tags_weight: 80
categories: ["docker"]
categories_weight: 80
keywords:
- redis
- docker
- 快速启动
- centos
- 容器化
- 数据库
description: "在 CentOS 系统上使用 Docker 容器快速部署和配置 Redis 数据库服务器的完整指南"

---

Redis 是一个高性能的内存数据库，广泛用于缓存、会话存储和消息队列等场景。本文将介绍如何在 CentOS 系统上使用 Docker 容器快速部署 Redis 服务器，让您在几分钟内就能拥有一个可用的 Redis 环境。

## 环境准备

在开始之前，请确保您的系统满足以下要求：

- **操作系统**: CentOS 7 或更高版本
- **Docker 版本**: 19.03.8 或更高版本
- **Redis 镜像**: redis:5.0.4（本教程使用的版本）
- **系统权限**: 需要 root 权限或 sudo 权限

## 部署步骤

### 第一步：系统优化配置

为了确保 Redis 能够稳定高效地运行，我们需要先对系统进行一些优化配置：

```bash
# 配置内存分配策略和网络连接数
echo 'vm.overcommit_memory=1
net.core.somaxconn=65535' >> /etc/sysctl.conf

# 应用配置
sysctl -p

# 禁用透明大页（提高 Redis 性能）
echo never > /sys/kernel/mm/transparent_hugepage/enabled
```

**配置说明**：
- `vm.overcommit_memory=1`：允许系统分配超过物理内存的虚拟内存，避免 Redis 因内存不足而崩溃
- `net.core.somaxconn=65535`：增加系统的最大连接数，提高并发处理能力
- 禁用透明大页可以避免 Redis 出现延迟抖动问题

### 第二步：下载 Redis 镜像

从 Docker Hub 拉取官方的 Redis 镜像：

```bash
docker pull redis:5.0.4
```

### 第三步：创建目录结构

为 Redis 创建配置文件和数据存储目录：

```bash
mkdir -p /application/redis/{conf,data}
```

这里创建了两个目录：
- `conf`：存放 Redis 配置文件
- `data`：存放 Redis 数据文件（持久化数据）

### 第四步：配置 Redis

下载并配置 Redis 配置文件：

```bash
# 下载官方配置文件
wget https://raw.githubusercontent.com/antirez/redis/5.0/redis.conf -O /application/redis/conf/redis.conf

# 配置日志输出
sed -i 's/logfile ""/logfile "access.log"/' /application/redis/conf/redis.conf

# 设置访问密码（请修改为您自己的密码）
sed -i 's/# requirepass foobared/requirepass your_secure_password/' /application/redis/conf/redis.conf

# 开启数据持久化
sed -i 's/appendonly no/appendonly yes/' /application/redis/conf/redis.conf

# 允许外部访问（生产环境请谨慎使用）
sed -i 's/bind 127.0.0.1/bind 0.0.0.0/' /application/redis/conf/redis.conf
```

**重要提示**：
- 请将 `your_secure_password` 替换为您自己设置的强密码
- 在生产环境中，建议限制 Redis 的访问来源，而不是绑定到 `0.0.0.0`

### 第五步：启动 Redis 容器

使用以下命令启动 Redis 容器：

```bash
docker run \
  -v /application/redis/conf/redis.conf:/etc/redis/redis.conf \
  -v /application/redis/data:/data \
  -p 6379:6379 \
  --restart=always \
  -m 8192M \
  --memory-swap 0 \
  --oom-kill-disable \
  --privileged=true \
  --name redis-server \
  -d redis:5.0.4 redis-server /etc/redis/redis.conf
```

**参数说明**：
- `-v /application/redis/conf/redis.conf:/etc/redis/redis.conf`：挂载配置文件
- `-v /application/redis/data:/data`：挂载数据目录，确保数据持久化
- `-p 6379:6379`：映射端口，允许外部访问
- `--restart=always`：容器自动重启，确保服务高可用
- `-m 8192M`：限制容器最大内存使用量为 8GB
- `--memory-swap 0`：禁用交换分区，提高性能
- `--oom-kill-disable`：禁用 OOM 杀死机制
- `--name redis-server`：为容器指定名称

## 验证部署

容器启动后，可以通过以下方式验证 Redis 是否正常运行：

```bash
# 查看容器状态
docker ps | grep redis-server

# 查看容器日志
docker logs redis-server

# 连接测试
docker exec -it redis-server redis-cli
```

在 Redis 命令行中，您可以执行以下测试命令：

```redis
# 认证（如果设置了密码）
AUTH your_secure_password

# 测试连接
PING

# 设置和获取数据
SET test "Hello Redis"
GET test
```

## 总结

通过以上步骤，您已经成功使用 Docker 部署了一个功能完整的 Redis 服务器。这种部署方式具有以下优势：

- **快速部署**：几分钟内即可完成部署
- **环境隔离**：容器化部署避免了环境冲突
- **易于管理**：通过 Docker 命令轻松管理服务
- **数据持久化**：数据存储在宿主机，容器重启不会丢失数据

在生产环境中使用时，建议进一步优化安全配置，如设置防火墙规则、使用更复杂的密码、配置 SSL/TLS 加密等。

