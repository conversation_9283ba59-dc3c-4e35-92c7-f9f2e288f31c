---
title: "Raid 阵列卡 Megacli 管理工具的使用记录"
date: 2021-02-24T20:54:13+08:00
draft: flase
tags: [ "raid", "stroage"]
tags_weight: 80
categories: ["linux"]
categories_weight: 80
keywords:
- raid
- megacli
- 阵列卡
- 管理
description: "Raid 阵列卡 Megacli 管理工具的使用记录"
---

# 安装

[**安装参考文档**](https://gist.github.com/fxkraus/595ab82e07cd6f8e057d31bc0bc5e779)

[**使用参考文档**](https://www.cnblogs.com/luxiaodai/p/9871612.html)

> 示例为： `Centos7 ` 中的安装步骤

```bash
wget https://docs.broadcom.com/docs-and-downloads/raid-controllers/raid-controllers-common-files/8-07-14_MegaCLI.zip

unzip 8-07-14_MegaCLI.zip

rpm -ivhU Linux/MegaCli-8.07.14-1.noarch.rpm 

cp -a /opt/MegaRAID/MegaCli/MegaCli64 /usr/local/bin/MegaCli
```


# 命令使用记录

## 将某块物理盘下线/上线

```
MegaCli -PDOffline -PhysDrv[31:8] -a0

MegaCli -PDRbld -ShowProg  -PhysDrv[31:8] `#[E:S]` -aAll  # 查看 rebuild 状态


MegaCli -pdrbld -progdsply -physdrv[31:8] -aALL  # 进度条显示 rebuild 状态


MegaCli -PDOnline -PhysDrv [1:4] -a0
```

**配置示例**

![image-20210514155136341](https://cdn.treesir.pub/img/image-20210514155136341.png)

## 查看磁盘是否有错误

```bash
MegaCli -PDList -aAll  |grep -i 'Error Count:'  # 查看磁盘是否有错误
```

## 查看磁盘状态

```bash
MegaCli -PDList -a0|grep 'Firmware state'
```

## 查看 RAID 卡 Rebuild 参数

```bash
MegaCli -AdpAllinfo -aALL | grep -i rebuild
```

##  显示所有逻辑磁盘组信息

```bash
MegaCli -LDInfo -Lall -aALL
```

## 显示所有的物理信息 

```bash
MegaCli -PDList -aAll
```

## 查看物理单块磁盘状态

```bash
MegaCli -PDRbld -ShowProg -PhysDrv [252:3] -a0
```

## 上下线磁盘

```bash
MegaCli -PDOffline -PhysDrv [252:3] -a0
MegaCli -PDOnline -PhysDrv [252:3] -a0
```
