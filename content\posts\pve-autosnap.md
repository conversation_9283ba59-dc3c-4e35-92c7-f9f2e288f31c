---
title: "PVE AutoSnap 自动快照工具使用指南"
date: 2023-10-05T16:40:38+08:00
draft: false
tags: [ "PVE", "Proxmox", "AutoSnap", "虚拟化"]
tags_weight: 80
categories: ["虚拟化", "备份"]
categories_weight: 80
keywords:
- PVE
- Proxmox
- AutoSnap
- 虚拟机快照
- 自动备份
- 虚拟化
description: "PVE AutoSnap 自动快照工具的安装配置和使用方法，实现虚拟机快照的自动化管理"
---

# 工具简介

PVE AutoSnap 是一个用于 Proxmox VE 环境的自动快照管理工具，可以自动创建、管理和清理虚拟机快照。

- **项目地址**：[cv4pve-autosnap](https://github.com/Corsinvest/cv4pve-autosnap)
- **主要功能**：自动创建虚拟机快照、设置保留策略、批量管理多个虚拟机

# 安装配置

## 下载并安装工具

```bash
# 下载最新版本
wget https://github.com/Corsinvest/cv4pve-autosnap/releases/download/v1.14.8/cv4pve-autosnap-linux-x64.zip

# 解压文件
unzip cv4pve-autosnap-linux-x64.zip

# 移动到系统路径
mv cv4pve-autosnap /usr/bin/

# 设置执行权限
chmod a+x /usr/bin/cv4pve-autosnap
```

> **注意**：请根据需要选择合适的版本，最新版本信息可查看 [GitHub Releases](https://github.com/Corsinvest/cv4pve-autosnap/releases)





# 基本使用

## 环境变量配置

首先设置 PVE 连接参数：

```bash
SNAP_LIST="111,101"          # 需要备份的虚拟机 ID 列表
PVE_USER='root@pam'          # PVE 用户名
PVE_PASSWD='your_password'   # PVE 密码
PVE_HOST='************'      # PVE 主机地址
```

## 创建虚拟机快照

批量为指定虚拟机创建快照：

```bash
for vmid in ${SNAP_LIST//,/ }; do
    cv4pve-autosnap --host=${PVE_HOST} \
    --username=${PVE_USER} \
    --password=${PVE_PASSWD} \
    --vmid=${vmid} \
    snap --label=daily --keep=3
done
```

**参数说明**：
- `--host`：PVE 主机地址
- `--username`：PVE 用户名
- `--password`：PVE 密码
- `--vmid`：虚拟机 ID
- `--label`：快照标签（如 daily、weekly）
- `--keep`：保留快照数量

## 查看快照状态

查看指定虚拟机的快照信息：

```bash
cv4pve-autosnap --host=${PVE_HOST} \
    --username=${PVE_USER} \
    --password=${PVE_PASSWD} \
    --vmid=111 status
```



# GitLab CI/CD 集成

## 创建执行脚本

创建 `Script.pve-snap` 脚本文件：

```bash
#!/bin/bash
#================================================================
# 文件名:    Script.pve-snap
# 版本:      v1.0
# 日期:      2023/9/25
# 作者:      yangzun
# 邮箱:      <EMAIL>
# 描述:      PVE 虚拟机快照自动化脚本
#================================================================

# 默认配置参数
SNAP_LIST=${SNAP_LIST:-'101'}
PVE_USER=${PVE_USER:-'root@pam'}
PVE_PASSWD=${PVE_PASSWD:-'your_password'}
PVE_HOST=${PVE_HOST:-'************'}

# 安装 AutoSnap 工具
function install_autosnap() {
    echo "正在安装 cv4pve-autosnap 工具..."
    wget https://github.com/Corsinvest/cv4pve-autosnap/releases/download/v1.14.8/cv4pve-autosnap-linux-x64.zip
    unzip cv4pve-autosnap-linux-x64.zip
    mv cv4pve-autosnap /usr/bin/
    chmod a+x /usr/bin/cv4pve-autosnap
    echo "cv4pve-autosnap 工具安装完成"
}

# 执行快照操作
function execute_snapshot() {
    echo "开始执行虚拟机快照操作..."
    for vmid in ${SNAP_LIST//,/ }; do
        echo "正在为虚拟机 ${vmid} 创建快照..."
        cv4pve-autosnap --host=${PVE_HOST} \
        --username=${PVE_USER} \
        --password=${PVE_PASSWD} \
        --vmid=${vmid} \
        snap --label=daily --keep=3
    done
    echo "快照操作执行完成"
}

# 查看快照状态
function check_snapshot_status() {
    echo "正在检查虚拟机快照状态..."
    for vmid in ${SNAP_LIST//,/ }; do
        echo "虚拟机 ${vmid} 的快照状态："
        cv4pve-autosnap --host=${PVE_HOST} \
        --username=${PVE_USER} \
        --password=${PVE_PASSWD} \
        --vmid=${vmid} \
        status
    done
}

# 主程序逻辑
case "${1}" in
    'exec')
        install_autosnap
        execute_snapshot
        ;;
    'plan')
        install_autosnap
        check_snapshot_status
        ;;
    *)
        echo "用法: $0 {exec|plan}"
        echo "  exec - 执行快照创建"
        echo "  plan - 查看快照状态"
        exit 1
        ;;
esac
```
  
  

## GitLab CI/CD 配置文件

创建 `.gitlab-ci.yml` 配置文件：

```yaml
# 全局变量配置
variables:
  extends: .default_vars

# 计划阶段 - 查看快照状态
.plan:
  stage: plan
  extends:
    - .schedule_trigger_disable

# 执行阶段 - 创建快照
.exec:
  stage: exec
  extends:
    - .master_schedule_manual_trigger

# 公共扩展配置
.extends_group:
  extends:
    - .runner_tag
    - .harbor_auth_vars
    - .load_function_before_jobs
    - .script_exec_vars
    - .nexus_auth_vars

# 计划脚本模板
.plan_script: &plan_script
  extends:
    - .plan
    - .extends_group
  script:
    - set -- $CI_JOB_NAME
    - chmod a+x Script.$1
    - ./Script.$1 plan

# 执行脚本模板
.exec_script: &exec_script
  extends:
    - .exec
    - .extends_group
  script:
    - set -- $CI_JOB_NAME
    - chmod a+x Script.$1
    - ./Script.$1 exec

# 任务定义
pve-snap plan: *plan_script   # 预览快照状态
pve-snap exec: *exec_script   # 执行快照创建
```

## 运行效果展示

### 计划阶段执行结果
![image-20231005164932661](https://cdn.treesir.pub/images/2023/10/05/20231005164934.png)

### 执行阶段运行结果
![image-20231005165104628](https://cdn.treesir.pub/images/2023/10/05/20231005165108.png)

# 使用建议

## 最佳实践

1. **定期备份**：建议设置定时任务，每日自动执行快照创建
2. **保留策略**：根据存储空间合理设置快照保留数量
3. **监控告警**：配置监控，及时发现快照创建失败的情况
4. **测试恢复**：定期测试快照恢复功能，确保备份有效性

## 注意事项

1. **权限配置**：确保 PVE 用户具有足够的权限执行快照操作
2. **存储空间**：监控存储空间使用情况，避免因空间不足导致快照失败
3. **网络连接**：确保执行环境能够正常访问 PVE 主机
4. **密码安全**：建议使用环境变量或密钥管理工具存储敏感信息

## 故障排除

- **连接失败**：检查网络连接和认证信息
- **权限不足**：确认用户权限配置
- **存储空间**：检查存储空间是否充足
- **虚拟机状态**：确认虚拟机处于可快照状态
