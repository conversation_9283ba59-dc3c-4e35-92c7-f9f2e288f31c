---
title: "kubernetes 集群中 控制平面 组件频繁发生重启的问题排查记录"
date: 2020-12-18T16:24:43+08:00
draft: false
tags: [ "coredns", "flannel", "fix"]
tags_weight: 60
categories: ["k8s"]
categories_weight: 60
keywords:
- Flannel
- Core
- 错误修复
- io high
description: "kubernetes集群中controller-manager及scheduler组件频繁重启的一次排查解决"
---
# 环境说明
  > - Kubernetes 版本: v1.19.6
  > - 使用操作系统: Centos-7.9.2009
## 现象说明
> 最近在使用k8s集群的时候，发现集群变慢了许多，排查发现 `master` 节点中 `controller-manager` 及 `scheduler` 组件频繁的发生重启
> ![image-20201222213306595](https://cdn.treesir.pub/images/2020/12/22/image-20201222213306595.png)

# 排查记录
## 抓取日志
> 使用重定向将日志写入至文件中再慢慢分析 (一开始使用前台抓取，打印日志太长，超出终端的默认显示行）
```bash
kubectl logs -f kube-controller-manager-master01 -n kube-system >> controller.log
```
### 错误信息如下所示
![image-20201222213552227](https://cdn.treesir.pub/images/2020/12/22/image-20201222213552227.png)

> 通过搜索引擎，有人说是etcd性能导致，重试使用了[`此文档文档`](https://github.com/etcd-io/etcd/blob/master/Documentation/tuning.md) 进行了etcd的优化，没有什么效果。

## 检测网络及io
### 安装工具
```bash
yum install dstat iotop -y

iotop -oP  # 检查 io

dstat -nf # 显示所有网络接口使用情况
dstat -df # 显示所有磁盘使用情况，  当接口过多时 可使用 "-N" 加指定的网口, "-D" 加磁盘 
```
> 排除得到 io 占用高且被flanneld这个进程占用着
![image-20201222215836738](https://cdn.treesir.pub/images/2020/12/22/image-20201222215836738.png)

### 找到问题
**发现社区也有人反馈这个问题，但是目前暂没有人员回复**
![image-20201222221012316](https://cdn.treesir.pub/images/2020/12/22/image-20201222221012316.png)

```bash
kubectl delete -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml

ifconfig cni0 down
ip link delete cni0
ifconfig flannel.1 down
ip link delete flannel.1
rm -rf /var/lib/cni/
rm -f /etc/cni/net.d/*
systemctl restart kubelet
```
> `删除flannel后` 各节点的io恢复正常，原因应该就是这个了，但是flannel组件不可缺少，得想个办法将它再次安装上才行。

## 更换版本
### 检查正在使用的版本
![image-20201222221312175](https://cdn.treesir.pub/images/2020/12/22/image-20201222221312175.png)

### 降级版本
```
curl https://raw.githubusercontent.com/coreos/flannel/v0.13.0/Documentation/kube-flannel.yml|sed 's#**********/16#**********/16#g' | kubectl apply -f -  # 我这里进行了替换子网，如没有修改默认的pod子网地址请直接使用下面语句即可

kubectl apply -f  https://raw.githubusercontent.com/coreos/flannel/v0.13.0/Documentation/kube-flannel.yml

watch kubectl get pod -n kube-system  # 等待 pod 启动完成
```
> 启动完成后再次使用 `iotop -oP` 观察io是否异常, 确认正常后观察 `controller-manager` 是否会再次发生重启
> ```bash
> kubectl logs -f kube-controller-manager-master01 -n kube-system > controller.log
> ```

## 绑定网卡

> 降低 flannel 的版本后，还是会出现 io 较高的情况。 后测试出，是因为我的集群机器中 `存在着多张网卡`，解决方法就是: 在 flannel 资源清单中添加一下 `--iface=ethX` 来绑定一下网卡 ，这里就要求集群中每一个节点 `多存在` 此网卡。

```bash
wget https://raw.githubusercontent.com/coreos/flannel/v0.13.0/Documentation/kube-flannel.yml

vim kube-flannel.yml
```

修改后的配置展示

```bash
      containers:
      - name: kube-flannel
        image: quay.io/coreos/flannel:v0.13.0
        command:
        - /opt/bin/flanneld
        args:
        - --ip-masq
        - --kube-subnet-mgr
        - --iface=eth0
```

再次部署创建

```bash
kubectl apply -f kube-flannel.yml
```

> 执行后，没有再次出现 io 占用高的情况了
