---
title: "CentOS 7 编译安装 QEMU 及 libvirt 管理工具"
date: 2021-02-25T09:07:25+08:00
draft: false
tags: [ "kvm", "Centos7"]
tags_weight: 80
categories: ["vm"]
categories_weight: 80
keywords:
- CentOS 7
- QEMU
- libvirt
- 编译安装
- KVM
description: "在 CentOS 7 系统上源码编译安装 QEMU，安装 libvirt 管理工具和 KVM 管理工具"
---



#  环境说明

- **操作系统**：CentOS 7.9.2009
- **QEMU 版本**：[5.2.0](https://download.qemu.org/qemu-5.2.0.tar.xz)
- **libvirt 版本**：5.4.0

> **注意**：libvirt 高版本安装较为复杂，如需安装高版本请参考[官方文档](https://libvirt.org/compiling.html)

# 编译安装 QEMU

> **注意**：以下操作命令主要在 `/data` 目录下进行。

## 获取源码

> 国内用户如下载较慢，可使用[此链接](https://cdn.treesir.pub/application/qemu/qemu-5.2.0.tar.xz)下载

```bash
wget https://download.qemu.org/qemu-5.2.0.tar.xz

tar xf qemu-5.2.0.tar.xz
```

## 安装依赖

### 安装软件依赖

```bash
yum -y install gcc gcc-c++ automake libtool zlib-devel glib2-devel bzip2-devel libuuid-devel spice-protocol spice-server-devel usbredir-devel libaio-devel wget python3 bzip2
```

### 安装 Ninja 构建工具

> Ninja 是 Google 推出的注重速度的构建工具。相比传统的 make/makefile，Ninja 通过并行组织编译任务大大提高了构建速度。QEMU 现在采用基于 Ninja 的构建系统，因此需要安装 Ninja。

```bash
git clone git://github.com/ninja-build/ninja.git && cd ninja

./configure.py --bootstrap

cp ninja /usr/bin/

ninja --version
1.10.2.git
```



## 配置编译参数

```bash
cd /data

mkdir qemu-build && cd qemu-build

../qemu-5.2.0/configure --enable-kvm --target-list="arm-softmmu i386-softmmu x86_64-softmmu arm-linux-user i386-linux-user x86_64-linux-user" --enable-debug  --cpu=x86_64
```

**参数说明**：
- `--enable-kvm`：启用 KVM 支持
- `--target-list=<架构名>`：指定编译的 CPU 架构，'x86_64-softmmu' 表示目标为 x86 64位 CPU
- `--enable-debug`：启用调试功能
- `--cpu`：指定本机 CPU 架构



## 编译安装

```bash
make -j$(getconf _NPROCESSORS_ONLN) \
&& make install
```

## 创建软链接

```bash
ln -sf /usr/local/bin/qemu-system-x86_64 /usr/bin/qemu-kvm
ln -sf /usr/local/bin/qemu-system-x86_64 /usr/libexec/qemu-kvm
```

## 验证安装

```bash
# qemu-img --version
qemu-img version 5.2.0
Copyright (c) 2003-2020 Fabrice Bellard and the QEMU Project developers

# qemu-kvm --version
QEMU emulator version 5.2.0
Copyright (c) 2003-2020 Fabrice Bellard and the QEMU Project developers
```

# 编译安装 libvirt 管理工具

## 安装构建依赖

```bash
yum -y install virt-install

yum -y install libnl-devel libxml2-devel yajl-devel device-mapper-devel libpciaccess-devel gnutls* libxslt libtirpc-devel libacl-devel libacl

pip3 install rst2html5
```

## 安装 Meson 构建工具

> Meson 基于 Python 3 实现，至少需要 Python 3.5 才能运行，默认采用 Ninja 作为后端。此步骤在上面安装 QEMU 时已配置 Ninja。

```bash
pip3 install meson

meson --version
0.57.1
```

## 下载源码

版本列表[地址](https://libvirt.org/sources/)

> 国内用户如下载较慢，可使用[此链接](https://cdn.treesir.pub/application/qemu/libvirt-5.4.0.tar.xz)下载

```bash
wget https://libvirt.org/sources/libvirt-5.4.0.tar.xz
```

## 编译安装

```bash
tar xf libvirt-5.4.0.tar.xz

cd libvirt-5.4.0/

./autogen.sh --system

make -j$(getconf _NPROCESSORS_ONLN) \
&& make install
```

## 安装管理工具

```bash
yum -y install virt-manager virt-viewer bridge-utils

yum -y install libXdmcp libxkbfile xorg-x11-xkb-utils
```

## 验证版本

```bash
virsh version
根据库编译：libvirt 5.4.0
使用库：libvirt 5.4.0
使用的 API: QEMU 5.4.0
运行管理程序: QEMU 5.2.0
```

## 启动服务并设置开机自启

```bash
service libvirtd start \
&& systemctl enable libvirtd

$ virsh list --all
 Id    名称                         状态
----------------------------------------------------
```

## 创建网桥

> 示例将 `ens27f0` 网卡配置为桥接网卡

```bash
cp  /etc/sysconfig/network-scripts/ifcfg-ens27f0{,.bak}  # 修改前备份配置文件

cat > /etc/sysconfig/network-scripts/ifcfg-ens27f0 << EOF
DEVICE=ens27f0
TYPE=Ethernet
BOOTPROTO=none
ONBOOT=yes
NM_CONTROLLED=no
BRIDGE=br0
EOF

cat > /etc/sysconfig/network-scripts/ifcfg-br0 << EOF
DEVICE="br0"
BOOTPROTO="static"
IPADDR="************"
NETMASK="*************"
GATEWAY="***********"
DNS1="***********12"
ONBOOT="yes"
TYPE="Bridge"
NM_CONTROLLED="no"
EOF

service network restart  # 重启网络服务，注意：此操作有风险，请确认配置无误后谨慎操作
```

![image-20210225100545161](https://cdn.treesir.pub/img/image-20210225100545161.png)



# 验证安装

> 使用其他机器上的模板虚拟机进行测试。

```bash
mkdir -p  /data/KvmData /data/qemu-xml # 创建虚拟机文件存放目录
```

## 方法一：导入现有虚拟机

在另一台机器上导出虚拟机定义文件，并复制到测试机器：

```bash
virsh dumpxml template > template.xml  # 导出虚拟机定义文件

scp template.xml ************:/data/qemu-xml  # 复制虚拟机元数据文件到测试机

scp /data/KvmData/template.img ************:/data/KvmData # 复制虚拟机磁盘文件到测试机

virsh define --file ./template.xml

virsh start template
```
![image-20210225102220521](https://cdn.treesir.pub/img/image-20210225102220521.png)

## 方法二：创建新虚拟机

```bash
mkdir -p /data/KvmData

qemu-img create -f raw /data/KvmData/centos7_0.raw 40G

virt-install --name centos7 \
--virt-type kvm \
--vcpus 2 \
--ram 2048 \
--cdrom=/data/iso/CentOS-7-x86_64-Minimal-2009.iso \
--disk=/data/KvmData/centos7_0.raw,cache=none \
--network bridge=br0  \
--os-type linux \
--os-variant rhel7 \
--graphics vnc,listen=0.0.0.0 \
--noautoconsole
```

![image-20210319174036305](https://cdn.treesir.pub/img/image-20210319174036305.png)

## 使用 VNC 远程连接

![image-20210319174617825](https://cdn.treesir.pub/img/image-20210319174617825.png)

> 默认无需账号和密码，直接回车即可连接。

