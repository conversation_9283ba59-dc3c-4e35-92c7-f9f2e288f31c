---
title: "VMware ESXi 虚拟机克隆完整操作指南"
date: 2021-02-21T10:09:38+08:00
draft: false
tags: [ "vmware", "esxi", "虚拟化", "克隆"]
tags_weight: 80
categories: ["虚拟化"]
categories_weight: 80
keywords:
- vmware
- esxi
- 虚拟机克隆
- vmkfstools
- 虚拟化管理
description: "详细介绍在 VMware ESXi 6.7 环境中使用命令行工具克隆虚拟机的完整流程和最佳实践"
---

在 VMware ESXi 环境中，虚拟机克隆是一个常见的运维操作，可以快速创建相同配置的虚拟机实例。本文将详细介绍如何在 ESXi 6.7 环境中使用命令行工具进行虚拟机克隆操作。

## 虚拟机克隆概述

### 什么是虚拟机克隆

虚拟机克隆是指创建现有虚拟机的完整副本，包括：
- **虚拟磁盘**: 完整复制源虚拟机的磁盘数据
- **配置文件**: 虚拟机的硬件配置信息
- **操作系统**: 已安装的操作系统和应用程序

### 克隆的优势

- **快速部署**: 避免重复安装操作系统和应用程序
- **环境一致性**: 确保多个虚拟机具有相同的基础配置
- **节省时间**: 大幅减少虚拟机创建时间
- **标准化**: 建立标准的虚拟机模板

## 环境信息

### 实验环境配置

| 项目 | 配置信息 | 说明 |
|------|----------|------|
| ESXi 版本 | 6.7 | VMware vSphere Hypervisor |
| 源虚拟机 | centos-source | 待克隆的模板虚拟机 |
| 目标虚拟机 | jira-confluence | 克隆后的新虚拟机 |
| 存储路径 | `/vmfs/volumes/1t-data/` | 虚拟机文件存储位置 |

### 源虚拟机信息

![源虚拟机文件结构](https://cdn.treesir.pub/img/image-20210221101709404.png)

*图：源虚拟机的文件结构，包含 VMDK 磁盘文件和配置文件*

## 操作步骤

### 第一步：启用 ESXi SSH 服务

为了使用命令行工具进行克隆操作，需要先启用 ESXi 的 SSH 服务：

#### 通过 Web 界面启用

1. 登录 ESXi Web 管理界面
2. 导航到 "主机" → "管理" → "服务"
3. 找到 "TSM-SSH" 服务并启动

![启用 SSH 服务](https://cdn.treesir.pub/img/image-20210221102103955.png)

*图：在 ESXi Web 界面中启用 SSH 服务*

#### 连接验证

启用 SSH 服务后，可以使用 SSH 客户端连接到 ESXi 主机：

```bash
# 使用 SSH 连接到 ESXi 主机
ssh root@esxi-host-ip
```

![SSH 连接示例](https://cdn.treesir.pub/img/image-20210221102208841.png)

*图：使用 SSH 客户端成功连接到 ESXi 主机*

**安全提示**:
- 克隆操作完成后，建议关闭 SSH 服务以提高安全性
- 使用强密码保护 root 账户
- 考虑使用 SSH 密钥认证替代密码认证

### 第二步：准备克隆环境

#### 创建目标虚拟机目录

在开始克隆之前，需要为新虚拟机创建专门的存储目录：

```bash
# 创建目标虚拟机的存储目录
mkdir -p /vmfs/volumes/1t-data/jira-confluence

# 验证目录创建成功
ls -la /vmfs/volumes/1t-data/
```

#### 检查存储空间

确保目标存储有足够的空间来存放克隆的虚拟机：

```bash
# 检查存储空间使用情况
df -h /vmfs/volumes/1t-data/

# 查看源虚拟机磁盘大小
ls -lh /vmfs/volumes/1t-data/centos-7-source/
```

### 第三步：克隆虚拟机磁盘

#### vmkfstools 工具介绍

`vmkfstools` 是 ESXi 提供的强大的虚拟磁盘管理工具，支持多种操作：
- 创建、克隆、转换虚拟磁盘
- 调整虚拟磁盘大小
- 管理 VMFS 文件系统

**官方文档**: [VMware KB 1028042](https://kb.vmware.com/s/article/1028042?lang=zh_CN)

#### 执行磁盘克隆

使用 vmkfstools 命令克隆虚拟机磁盘：

```bash
vmkfstools -i "/vmfs/volumes/1t-data/centos-7-source/centos-7-source.vmdk" \
           "/vmfs/volumes/1t-data/jira-confluence/jira-confluence.vmdk" \
           -d thin \
           -a buslogic
```

**参数说明**:
- `-i`: 指定源虚拟磁盘文件路径
- `-d thin`: 使用精简置备模式（节省存储空间）
- `-a buslogic`: 指定 SCSI 适配器类型为 BusLogic

**磁盘格式选项**:
- `thin`: 精简置备，按需分配存储空间
- `thick`: 厚置备，预先分配全部存储空间
- `eagerzeroedthick`: 厚置备置零，提供最佳性能和安全性

#### 监控克隆进度

克隆过程可能需要较长时间，可以通过以下方式监控进度：

```bash
# 查看克隆进程
ps aux | grep vmkfstools

# 监控目标目录大小变化
watch -n 5 'ls -lh /vmfs/volumes/1t-data/jira-confluence/'
```

![image-20210221102953651](https://cdn.treesir.pub/img/image-20210221102953651.png)

### 复制 虚拟机元数据

```bash
cp /vmfs/volumes/1t-data/centos-7-source/centos-7-source.vmx /vmfs/volumes/1t-data/jira-confluence/jira-confluence.vmx
```

> 最终文件展示
>
> ![image-20210221103201167](https://cdn.treesir.pub/img/image-20210221103201167.png)



## 修改元数据文件

修改 及 删除 项: 

- `scsi0:0.fileName`
- `displayName`

```bash
vi jira-confluence.vmx
```

![image-20210221103938512](https://cdn.treesir.pub/img/image-20210221103938512.png)

# 注册主机

![image-20210221104122066](https://cdn.treesir.pub/img/image-20210221104122066.png)

`找到刚才修改的元文件路径，右键注册主机`

![image-20210221104252834](https://cdn.treesir.pub/img/image-20210221104252834.png)

![image-20210221104321096](https://cdn.treesir.pub/img/image-20210221104321096.png)

`选择我已复制，确认。`

![image-20210221104343184](https://cdn.treesir.pub/img/image-20210221104343184.png)

![image-20210221104527987](https://cdn.treesir.pub/img/image-20210221104527987.png)

# done。。。
