# GitLab CI/CD 配置文件
# 用于自动化构建和部署 Hugo 博客到 GitLab Pages

# 定义阶段
stages:
  - build
  - docker-build
  - deploy

# 缓存配置
cache:
  paths:
    - .hugo_cache/

# 构建阶段
build:
  stage: build
  image: proxyhub.cpinnov.run/hugomods/hugo
  variables:
    HUGO_ENV: production
    HUGO_ENABLEGITINFO: "true"
  script:
    # 检查 Hugo 版本
    - hugo version
    - |
      # 配置下载对应主题
      rm -rf themes/blowfish
      git clone --depth 1 https://github.com/nunocoracao/blowfish.git themes/blowfish
    # 构建静态文件
    - hugo --minify --gc --enableGitInfo
    # 显示构建信息
    - echo "Build completed successfully!"
    - echo "Generated files:"
    - ls -la public/
  artifacts:
    paths:
      - public/
    expire_in: 1 hour
  only:
    - main
  tags:
    - release-http-proxy

# 部署到 GitLab Pages
pages:
  stage: deploy
  script:
    # 复制构建产物到 pages 目录
    - cp -r public/* .
    # 创建 .nojekyll 文件（避免 Jekyll 处理）
    - touch .nojekyll
    # 显示部署信息
    - echo "Deployment completed!"
    - echo "Files to be deployed:"
    - ls -la
  artifacts:
    paths:
      - .
    expire_in: 30 days
  dependencies:
    - build
  tags:
    - release-http-proxy
  only:
    - main

# Docker 镜像构建阶段
docker-build:
  stage: docker-build
  before_script:
    # 登录到 GitLab Container Registry
    - echo $CI_REGISTRY_PASSWORD | docker login -u $CI_REGISTRY_USER --password-stdin
  script:
    # 构建 Docker 镜像
    - |
      export IMAGE_TAG=yangzun/blog:$CI_COMMIT_SHA
      export LATEST_TAG=yangzun/blog:latest
    - echo "Building Docker image..."
    - mv public pages
    - docker build -t $IMAGE_TAG -t $LATEST_TAG .
    # 推送镜像到注册表
    - echo "Pushing Docker image to registry..."
    - docker push $IMAGE_TAG
    - docker push $LATEST_TAG
    # 显示构建信息
    - echo "Docker image built and pushed successfully!"
    - echo "Image tags:"
    - echo "  - $IMAGE_TAG"
    - echo "  - $LATEST_TAG"
  
  dependencies:
    - build
  tags:
    - release-http-proxy
  only:
    - main