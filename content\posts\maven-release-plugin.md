---
title: "Maven ReleasePlugin 的使用记录"
date: 2021-05-16T17:52:05+08:00
draft: false
tags: [ "maven","ci-cd" ]
tags_weight: 20
categories: ["devops"]
categories_weight: 20
keywords:
- maven
- ci-cd
- jenkins
- devops
- kubernetes
description: "maven release plugin 的使用记录"

---


## `maven`  settings.xml 配置使用展示

```xml
 <?xml version="1.0" encoding="UTF-8"?>
 <settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <!-- localRepository
              | The path to the local repository maven will use to store artifacts.
     |
     | Default: ${user.home}/.m2/repository
    <localRepository>/path/to/local/repo</localRepository>
    -->
   <pluginGroups>
     <!-- pluginGroup
                                    | Specifies a further group identifier to use for plugin lookup.
     <pluginGroup>com.your.plugins</pluginGroup>
     -->
   </pluginGroups>

   <!-- proxies
                            | This is a list of proxies which can be used on this machine to connect to the network.
    | Unless otherwise specified (by system property or command-line switch), the first proxy
    | specification in this list marked as active will be used.
    |-->
   <proxies>
   </proxies>

   <servers>
     <server>
       <id>user-snapshot</id>
       <username>yangzun</username>
       <password>12345678</password>
     </server>

     <server>
       <id>user-release</id>
       <username>yangzun</username>
       <password>12345678</password>
     </server>
   </servers>

   <mirrors>
      <mirror>
          <id>user-snapshot</id>
          <mirrorOf>*</mirrorOf>
          <url>http://mirror.treesir.pub/repository/maven-hub</url>
      </mirror>
      <mirror>
          <id>user-release</id>
          <mirrorOf>*</mirrorOf>
          <url>http://mirror.treesir.pub/repository/maven-hub</url>
      </mirror>
   </mirrors>

   <profiles>

    <profile>  
            <id>nexus</id>  
            <repositories>  
            <repository>  
                <id>central</id> 
                <name>_nexus</name> 
                <url>http://mirror.treesir.pub/repository/maven-hub/</url>  
                <releases><enabled>true</enabled></releases>  
                <snapshots><enabled>true</enabled></snapshots>  
          </repository>  
          </repositories>  
          <pluginRepositories>  
              <pluginRepository>  
              <id>central</id>  
              <name>_nexus</name>
              <url>http://mirror.treesir.pub/repository/maven-hub/</url>  
              <releases><enabled>true</enabled></releases>  
              <snapshots><enabled>true</enabled></snapshots>  
          </pluginRepository>  
          </pluginRepositories>  
    </profile>

   </profiles>

   <activeProfiles>
     <activeProfile>nexus</activeProfile>
   </activeProfiles>
 </settings>
```

# 插件打包的使用记录说明

> maven plugin 在打包过程中，是会基于当前分支，创建一个分支进行打包，并会将创建的分支 push 至远端。与 项目的 分支管理模式有所冲突，并不想push 至远端的话

- 指定构建版本为 `1.0.4` 的 release 制品

  ```bash
  mvn -B release:prepare \
  -DtagNameFormat=@{project.version} \
  -DreleaseVersion=1.0.4 \
  -DpushChanges=false \
  -Dmaven.test.skip=true \
  -T $(nproc) # 指定发布 "1.0.4" 的 release
  ```

- 将制品发布至远程 release 制品仓库

  ```bash
  mvn release:perform \
  -Darguments='-Dmaven.javadoc.skip=true' \
  -Dmaven.test.skip=true \
  -DlocalCheckout=true \
  -T $(nproc) # 上传至 maven 私服
  ```

# 参考文档

[https://maven.apache.org/maven-release/maven-release-plugin/plugin-info.html](https://maven.apache.org/maven-release/maven-release-plugin/plugin-info.html)

