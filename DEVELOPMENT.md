# 开发指南

本文档为开发者提供详细的本地开发指南和内容创作说明。

## 🛠️ 开发环境搭建

### 系统要求

- **操作系统**: macOS, Linux, Windows
- **Hugo**: Extended 版本 (推荐 v0.120+)
- **Git**: 版本控制
- **编辑器**: VS Code, Vim, 或其他支持 Markdown 的编辑器

### 安装 Hugo

#### macOS
```bash
# 使用 Homebrew
brew install hugo

# 验证安装
hugo version
```

#### Linux (Ubuntu/Debian)
```bash
# 下载最新版本
wget https://github.com/gohugoio/hugo/releases/download/v0.120.4/hugo_extended_0.120.4_linux-amd64.deb

# 安装
sudo dpkg -i hugo_extended_0.120.4_linux-amd64.deb

# 验证安装
hugo version
```

#### Windows
```bash
# 使用 Chocolatey
choco install hugo-extended

# 或下载安装包
# https://github.com/gohugoio/hugo/releases
```

## 🚀 本地开发

### 1. 克隆项目

```bash
<NAME_EMAIL>:devops/blowfish-blog.git
cd blowfish-blog
```

### 2. 启动开发服务器

```bash
# 启动开发服务器（包含草稿）
hugo server -D

# 启动开发服务器（不包含草稿）
hugo server

# 启动开发服务器（绑定到所有网络接口）
hugo server -D --bind 0.0.0.0

# 启动开发服务器（指定端口）
hugo server -D --port 8080
```

### 3. 访问网站

打开浏览器访问 [http://localhost:1313](http://localhost:1313)

### 4. 实时预览

Hugo 开发服务器支持实时预览：
- 修改内容后自动刷新
- 支持热重载
- 显示构建错误信息

## ✍️ 内容创作

### 创建新文章

#### 方法一：使用 Hugo 命令

```bash
# 创建新文章
hugo new posts/my-new-article.md

# 创建带日期的文章
hugo new posts/2024/01/my-new-article.md
```

#### 方法二：手动创建

在 `content/posts/` 目录下创建 `.md` 文件：

```markdown
---
title: "文章标题"
description: "文章描述"
date: 2024-01-01T10:00:00+08:00
lastmod: 2024-01-01T10:00:00+08:00
draft: false
tags: ["标签1", "标签2"]
categories: ["分类1"]
author: "Zayn"
showToc: true
TocOpen: true
weight: 1
---

文章内容...
```

### Front Matter 配置

#### 基本配置

```yaml
---
title: "文章标题"
description: "文章描述"
date: 2024-01-01T10:00:00+08:00
lastmod: 2024-01-01T10:00:00+08:00
draft: false
tags: ["Docker", "Kubernetes"]
categories: ["容器技术"]
author: "Zayn"
---
```

#### 高级配置

```yaml
---
title: "文章标题"
description: "文章描述"
date: 2024-01-01T10:00:00+08:00
lastmod: 2024-01-01T10:00:00+08:00
draft: false
tags: ["Docker", "Kubernetes"]
categories: ["容器技术"]
author: "Zayn"
showToc: true
TocOpen: true
weight: 1
featured: true
thumbnail: "featured-image.jpg"
aliases: ["/old-url/"]
---
```

### 内容格式

#### Markdown 语法

```markdown
# 一级标题
## 二级标题
### 三级标题

**粗体文本**
*斜体文本*
`代码片段`

[链接文本](https://example.com)

![图片描述](image.jpg)

> 引用文本

- 无序列表项
- 另一个列表项

1. 有序列表项
2. 另一个列表项
```

#### 代码块

````markdown
```bash
# Shell 命令
docker run -d nginx
```

```yaml
# YAML 配置
apiVersion: v1
kind: Pod
metadata:
  name: nginx
```

```python
# Python 代码
def hello_world():
    print("Hello, World!")
```
````

### Hugo 短代码

#### 警告框

```markdown
{{< alert "warning" >}}
这是一个警告信息。
{{< /alert >}}
```

#### 按钮

```markdown
{{< button href="/posts/" target="_self" >}}
查看所有文章
{{< /button >}}
```

#### 代码高亮

```markdown
{{< highlight bash >}}
docker run -d nginx
{{< /highlight >}}
```

#### 图表

```markdown
{{< mermaid >}}
graph TD
    A[开始] --> B{判断}
    B -->|是| C[执行]
    B -->|否| D[结束]
    C --> D
{{< /mermaid >}}
```

## 🎨 主题定制

### 自定义 CSS

在 `assets/css/custom.css` 中添加自定义样式：

```css
/* 自定义样式 */
.custom-class {
    color: #007bff;
    font-weight: bold;
}
```

### 自定义布局

在 `layouts/` 目录下创建自定义模板：

```
layouts/
├── _default/
│   ├── baseof.html
│   ├── list.html
│   └── single.html
├── partials/
│   ├── header.html
│   └── footer.html
└── shortcodes/
    └── custom.html
```

### 主题配置

在 `config/_default/` 目录下配置主题：

```toml
# config/_default/params.toml
[params]
  # 主题颜色
  colorScheme = "auto"
  
  # 显示设置
  showReadingTime = true
  showWordCount = true
  
  # 社交链接
  [params.social]
    github = "your-github"
    twitter = "your-twitter"
```

## 🔧 构建和测试

### 本地构建

```bash
# 构建生产版本
hugo --minify

# 构建包含草稿
hugo --minify --buildDrafts

# 构建包含未来文章
hugo --minify --buildFuture

# 构建并启用 Git 信息
hugo --minify --enableGitInfo
```

### 内容验证

```bash
# 检查配置
hugo config

# 检查内容语法
hugo --buildDrafts --buildFuture

# 检查链接
hugo --buildDrafts --buildFuture --gc
```

### 性能测试

```bash
# 构建时间测试
time hugo --minify

# 内存使用测试
hugo --minify --gc
```

## 📁 文件组织

### 推荐的文件结构

```
content/
├── posts/
│   ├── 2024/
│   │   ├── 01/
│   │   │   ├── article-1.md
│   │   │   └── assets/
│   │   │       └── image1.jpg
│   │   └── 02/
│   │       └── article-2.md
│   └── _index.md
├── about.md
└── _index.md

assets/
├── css/
│   └── custom.css
├── js/
│   └── custom.js
└── img/
    └── logo.png

static/
├── favicon.ico
├── robots.txt
└── sitemap.xml
```

### 图片管理

#### 文章图片

```markdown
<!-- 在文章中使用图片 -->
![图片描述](/posts/2024/01/article-1/image.jpg)
```

#### 全局图片

```markdown
<!-- 使用静态图片 -->
![Logo](/img/logo.png)
```

## 🚀 部署流程

### 1. 提交更改

```bash
# 添加文件
git add .

# 提交更改
git commit -m "Add new article: Docker deployment guide"

# 推送到远程仓库
git push origin main
```

### 2. 自动部署

- GitLab CI 自动触发构建
- 构建成功后自动部署到 Pages
- 网站将在几分钟内更新

### 3. 验证部署

- 访问网站确认更新
- 检查构建日志
- 测试功能是否正常

## 🐛 故障排除

### 常见问题

#### 1. Hugo 命令未找到

```bash
# 检查 Hugo 安装
which hugo
hugo version

# 重新安装 Hugo
brew reinstall hugo  # macOS
```

#### 2. 主题未找到

```bash
# 检查主题目录
ls themes/

# 重新安装主题
git submodule update --init --recursive
```

#### 3. 构建错误

```bash
# 检查配置
hugo config

# 详细错误信息
hugo --verbose
```

#### 4. 样式问题

```bash
# 清除缓存
hugo --gc

# 重新构建
hugo --minify --gc
```

### 调试技巧

1. **启用详细日志**
   ```bash
   hugo server --verbose
   ```

2. **检查构建产物**
   ```bash
   ls -la public/
   ```

3. **验证配置**
   ```bash
   hugo config
   ```

## 📚 学习资源

- [Hugo 官方文档](https://gohugo.io/documentation/)
- [Blowfish 主题文档](https://blowfish.page/docs/)
- [Markdown 语法指南](https://www.markdownguide.org/)
- [Git 使用指南](https://git-scm.com/doc)

---

如有问题，请提交 Issue 或联系维护者。 