---
title: "Docker 部署 OpenWrt 软路由及宿主机通信配置"
date: 2020-11-15T16:09:10+08:00
draft: false
tags: [ "n1","docker-compose","docker","openwrt"]
tags_weight: 88
categories: ["fix","openwrt"]
categories_weight: 88
keywords:
- 软路由
- docker
- openwrt
- fix
description: "基于 Docker 部署 OpenWrt 软路由系统，并解决 macvlan 模式下宿主机通信问题"
---

# 环境配置

本文档基于以下环境配置进行部署：

- **宿主机 IP**：*************
- **OpenWrt 容器 IP**：************* (macvlan 模式)
- **主路由网关**：***********
- **网络段**：***********/24
- **Docker 镜像**：`raymondwong/openwrt_r9:21.2.1-arm64`
- **操作系统**：ARMBIAN

# 部署 OpenWrt 软路由

## 1. 安装 Docker Compose

```bash
# Debian/Ubuntu 系统
apt install -y docker-compose

# CentOS/RHEL 系统
yum install -y docker-compose
```

## 2. 创建配置文件

首先创建工作目录并准备 Docker Compose 配置文件：

> 💡 **镜像说明**：本文使用 [raymondwong/openwrt_r9](https://hub.docker.com/r/raymondwong/openwrt_r9/tags?page=1&ordering=last_updated) 镜像

```bash
# 创建工作目录
mkdir -p /data/docker-compose/openwrt/
cd /data/docker-compose/openwrt/

# 查看网卡名称（用于后续配置）
ip a

# 创建 Docker Compose 配置文件
cat > docker-compose.yaml << EOF
version: '2'

services:
  openwrt:
    image: raymondwong/openwrt_r9:21.2.1-arm64  # ARM 架构专用，x86 需更换对应镜像
    container_name: openwrt_r9
    privileged: true
    restart: always
    networks:
      openwrt_macnet:
        ipv4_address: *************

networks:
  openwrt_macnet:
    driver: macvlan
    driver_opts:
      parent: eth0  # 请根据实际网卡名称修改
    ipam:
      config:
        - subnet: ***********/24
          ip_range: *************/25
          gateway: ***********
EOF
```

## 3. 启动容器

### 启用网卡混杂模式

```bash
ip link set eth0 promisc on
```

### 启动 OpenWrt 容器

```bash
docker-compose up -d
```

## 4. 配置网络参数

容器启动后，默认 IP 为 `*************`，需要调整为当前网段：

```bash
# 修改网络配置并重启容器
docker exec -it openwrt_r9 bash -c "sed -i 's#*************#*************#g;s#***********#***********#g' /etc/config/network" \
&& docker restart openwrt_r9

# 测试网络连通性
docker exec -it openwrt_r9 bash -c "ping -c 3 baidu.com"
```

![image-20210527102828331](https://cdn.treesir.pub/img/image-20210527102828331.png)

配置完成后，可通过浏览器访问 `http://*************` 进入 OpenWrt 管理界面。

> 📝 **默认登录信息**：用户名 `root`，密码 `password`

![image-20210527103005072](https://cdn.treesir.pub/img/image-20210527103005072.png)

# 解决宿主机通信问题

## 问题分析

在使用 Docker macvlan 模式部署 OpenWrt 时，会遇到宿主机与容器无法直接通信的问题。

### 技术原理

macvlan 模式通过在物理网卡上创建多个虚拟网卡实现网络隔离，每个虚拟网卡拥有独立的 MAC 地址，可以获得不同的 IP 地址。这种模式的优势是容器可以直接接入物理网络，但出于安全考虑，**macvlan 默认禁止宿主机与容器之间的直接通信**。

### 解决思路

通过在宿主机创建额外的 macvlan 接口，并配置相应路由规则，使宿主机与容器的通信通过 macvlan 接口进行转发。由于 macvlan 接口之间可以正常通信，从而解决连通性问题。

## 配置步骤

> ⚠️ **注意**：以下所有操作均在宿主机上执行

### 1. 创建 macvlan 接口

创建名为 `mynet` 的 macvlan 接口：

```bash
ip link add mynet link eth0 type macvlan mode bridge
```

> 💡 **提示**：接口名称 `mynet` 可自定义，但不要与容器内的网卡重名

### 2. 配置接口参数

为新创建的接口分配 IP 地址并启用：

```bash
# 分配 IP 地址
ip addr add ***********0 dev mynet

# 启用接口
ip link set mynet up
```

### 3. 添加路由规则

配置静态路由，使宿主机访问 OpenWrt 容器的流量通过 `mynet` 接口：

```bash
ip route add ************* dev mynet
```

### 4. 验证连通性

测试宿主机与 OpenWrt 容器的网络连通性：

```bash
# 从容器内 ping 宿主机
docker exec -it openwrt_r9 ping ************* -c 3

# 预期输出示例：
# PING ************* (*************): 56 data bytes
# 64 bytes from *************: seq=0 ttl=64 time=0.508 ms
```

### 5. 配置开机自启

为确保重启后配置依然生效，将命令添加到开机启动脚本：

```bash
cat >> /etc/rc.local << 'EOF'
# Docker OpenWrt macvlan 通信配置
ip link add mynet link eth0 type macvlan mode bridge
ip addr add ***********0 dev mynet
ip link set mynet up
ip route add ************* dev mynet
EOF
```

![image-20210527103450010](https://cdn.treesir.pub/img/image-20210527103450010.png)

### 6. 设置执行权限

确保启动脚本具有可执行权限：

```bash
chmod +x /etc/rc.local
```

## 总结

通过以上配置，成功解决了 Docker macvlan 模式下宿主机与 OpenWrt 容器的通信问题。该方案的核心是利用 macvlan 接口间的互通特性，通过路由转发实现网络连通。

# 参考资料

- [Using Docker macvlan networks](https://blog.oddbit.com/post/2018-03-12-using-docker-macvlan-networks/) - Docker macvlan 网络详细说明

