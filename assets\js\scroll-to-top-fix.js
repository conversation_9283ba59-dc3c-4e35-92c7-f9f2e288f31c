// 修复回到顶部按钮显示问题
(function() {
  'use strict';
  
  function ensureScrollToTopVisible() {
    const scroller = document.getElementById("top-scroller");
    if (scroller) {
      // 强制显示按钮，覆盖主题的隐藏逻辑
      scroller.hidden = false;
      scroller.style.display = 'block';
      scroller.style.visibility = 'visible';
      scroller.style.opacity = '1';
      
      // 确保按钮在正确位置
      scroller.style.position = 'fixed';
      scroller.style.bottom = '2rem';
      scroller.style.right = '2rem';
      scroller.style.zIndex = '50';
      
      console.log('Scroll-to-top button forced to be visible');
    }
  }
  
  // 页面加载完成后立即执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', ensureScrollToTopVisible);
  } else {
    ensureScrollToTopVisible();
  }
  
  // 使用MutationObserver监听DOM变化，防止按钮被隐藏
  if (typeof MutationObserver !== 'undefined') {
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.type === 'attributes' && mutation.attributeName === 'hidden') {
          const scroller = document.getElementById("top-scroller");
          if (scroller && scroller.hidden) {
            console.log('Detected hidden attribute, forcing visibility');
            ensureScrollToTopVisible();
          }
        }
      });
    });
    
    // 开始观察
    const scroller = document.getElementById("top-scroller");
    if (scroller) {
      observer.observe(scroller, {
        attributes: true,
        attributeFilter: ['hidden', 'style']
      });
    }
  }
  
  // 定期检查按钮状态（备用方案）
  setInterval(function() {
    const scroller = document.getElementById("top-scroller");
    if (scroller && (scroller.hidden || scroller.style.display === 'none')) {
      console.log('Periodic check: forcing button visibility');
      ensureScrollToTopVisible();
    }
  }, 1000);
  
})();
