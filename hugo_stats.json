{"htmlElements": {"tags": ["a", "article", "body", "button", "code", "details", "div", "footer", "form", "h1", "h2", "h3", "h4", "head", "header", "hr", "html", "input", "li", "link", "main", "meta", "nav", "ol", "p", "path", "pre", "script", "section", "span", "strong", "summary", "svg", "time", "title", "ul"], "classes": ["!no-underline", "-mx-2", "-my-2", "-translate-y-8", "absolute", "align-text-bottom", "anchor", "appearance-none", "article", "article-content", "backdrop-blur", "backdrop-blur-2xl", "backdrop-blur-sm", "bg-gradient-to-b", "bg-neutral", "bg-neutral-100", "bg-neutral-100/50", "bg-neutral-500/50", "bg-neutral/50", "bg-primary-200", "bg-transparent", "block", "border", "border-dotted", "border-neutral-200", "border-neutral-300", "border-primary-400", "bottom-0", "chroma", "cursor-default", "cursor-pointer", "dark:bg-neutral-600", "dark:bg-neutral-700", "dark:bg-neutral-800", "dark:bg-neutral-800/50", "dark:bg-neutral-900/50", "dark:border-neutral-600", "dark:border-neutral-700", "dark:border-primary-600", "dark:flex", "dark:from-neutral-800", "dark:group-hover:text-neutral-700", "dark:group-hover:text-primary-400", "dark:hidden", "dark:hover:text-primary-400", "dark:prose-invert", "dark:scrollbar-thumb-neutral-600", "dark:scrollbar-track-neutral-800", "dark:text-neutral", "dark:text-neutral-100", "dark:text-neutral-300", "dark:text-neutral-400", "dark:text-neutral-500", "dark:text-primary-400", "dark:underline-neutral-600", "decoration-neutral-300", "decoration-primary-500", "fa-search", "fa-w-16", "first:mt-8", "fixed", "flex", "flex-1", "flex-auto", "flex-col", "flex-none", "flex-row", "flex-wrap", "focus:outline-2", "focus:outline-dotted", "focus:outline-transparent", "focus:translate-y-0", "font-bg", "font-bold", "font-extrabold", "font-medium", "font-normal", "font-semibold", "font-sm", "from-60%", "from-neutral", "gap-x-3", "gap-x-5", "group", "group-hover:decoration-primary-500", "group-hover:opacity-100", "group-hover:text-primary-300", "group-hover:text-primary-600", "group-hover:underline", "grow", "h-12", "h-8", "h-full", "h-screen", "hidden", "highlight", "hover:decoration-2", "hover:decoration-primary-400", "hover:text-gray-900", "hover:text-primary-500", "hover:text-primary-600", "hover:underline", "hover:underline-offset-2", "icon", "inline", "inline-block", "inset-0", "inset-x-0", "invisible", "items-center", "items-end", "justify-between", "justify-center", "leading-6", "leading-7", "lg:block", "lg:flex-row", "lg:hidden", "lg:ml-auto", "lg:order-last", "lg:p-[12vh]", "lg:px-32", "lg:sticky", "lg:top-[140px]", "lg:w-1/4", "list-none", "ltr:-left-6", "ltr:-ml-5", "ltr:border-l", "ltr:hidden", "ltr:inline", "ltr:lg:pl-8", "ltr:mr-1", "ltr:pl-5", "ltr:pr-2", "ltr:right-0", "ltr:sm:last:mr-0", "ltr:sm:mr-7", "ltr:text-right", "m-0", "m-auto", "main-menu", "max-w-3xl", "max-w-7xl", "max-w-[64rem]", "max-w-fit", "max-w-full", "max-w-prose", "mb-1", "mb-12", "mb-16", "mb-2", "mb-20", "mb-3", "mb-5", "mb-6", "md:flex", "md:flex-nowrap", "md:hidden", "md:justify-start", "md:ml-12", "md:mt-0", "md:p-[10vh]", "md:px-24", "md:w-1/3", "menuhide", "min-h-0", "min-h-[130px]", "min-h-[148px]", "min-w-0", "min-w-[220px]", "mix-blend-normal", "ml-3", "ml-auto", "mr-2", "mr-3", "mr-auto", "mt-0", "mt-1", "mt-12", "mt-2", "mt-3", "mt-5", "mt-8", "mt-[0.1rem]", "mt-[0.5rem]", "mx-1", "mx-auto", "my-3", "nested-menu", "not-prose", "nozoom", "opacity-0", "opacity-65", "order-first", "overflow-auto", "overflow-hidden", "overflow-visible", "overflow-y-auto", "overscroll-contain", "p-4", "p-5", "pb-4", "pb-[3px]", "pl-0", "pl-[24px]", "pointer-events-auto", "pointer-events-none", "pr-0", "pr-[24px]", "print:hidden", "prose", "pt-2", "pt-3", "pt-8", "pt-[2px]", "pt-[5px]", "px-0", "px-1", "px-2", "px-3", "px-4", "px-6", "py-1", "py-10", "py-2", "py-6", "py-[1px]", "relative", "rounded-b-lg", "rounded-full", "rounded-lg", "rounded-md", "rounded-xl", "rtl:-mr-5", "rtl:-right-6", "rtl:border-r", "rtl:hidden", "rtl:inline", "rtl:left-0", "rtl:lg:pr-8", "rtl:ml-1", "rtl:pl-2", "rtl:pr-5", "rtl:sm:last:ml-0", "rtl:sm:ml-7", "rtl:text-left", "scroll-smooth", "scrollbar-thin", "scrollbar-thumb-neutral-400", "scrollbar-track-neutral-200", "self-center", "shadow-2xl", "shadow-lg", "single_hero_background", "sm:flex-row", "sm:mb-0", "sm:p-6", "sm:px-14", "sm:px-6", "sm:w-1/2", "space-x-3", "space-y-10", "space-y-2", "space-y-3", "sticky", "svg-inline--fa", "text-2xl", "text-4xl", "text-base", "text-bg", "text-gray-500", "text-left", "text-lg", "text-neutral-400", "text-neutral-500", "text-neutral-700", "text-neutral-800", "text-neutral-900", "text-primary-500", "text-primary-600", "text-primary-700", "text-right", "text-sm", "text-xl", "text-xs", "to-transparent", "toc", "toc-inside", "toc-right", "top-0", "top-20", "top-[110vh]", "top-[calc(100vh-5.5rem)]", "transition-opacity", "w-12", "w-6", "w-8", "w-full", "w-screen", "xl:w-1/4", "z-10", "z-100", "z-30", "z-500", "z-80"], "ids": ["1-create-a-new-site", "1-custom-css", "1-custom-shortcodes", "1-多种首页布局", "2-add-a-theme", "2-custom-layouts", "2-mermaid-diagrams", "2-配色方案", "3-content-features", "3-create-your-first-post", "3-custom-color-scheme", "3-math-equations", "4-image-galleries", "4-navigation--menus", "4-start-the-development-server", "5-seo--performance", "TOCView", "TableOfContents", "advanced-features", "appearance-switcher", "appearance-switcher-mobile", "author-configuration", "basic-configuration", "best-practices", "blowfish-主题特色", "build-for-production", "building-and-deployment", "close-search-button", "conclusion", "configuration", "configuration-examples", "content-management", "content-organization", "creating-your-first-site", "customization-tips", "deployment-options", "front-matter", "hugo-directory-structure", "hugo-入门初学者指南", "hugo-和-blowfish-入门", "hugo-的主要优势", "hugo-的优势", "linux", "macos", "main-content", "menu-blur", "menu-button", "menu-close-button", "menu-configuration", "menu-wrapper", "rich-content-support", "script-bundle", "search-button", "search-button-mobile", "search-modal", "search-query", "search-results", "search-wrapper", "single_header", "site-footer", "social-integration", "the-top", "tips-for-success", "top-scroller", "windows", "为什么选择-blowfish", "为什么选择-hugo-和-blowfish", "主要功能", "什么是-hugo", "博客文章", "安装", "总结", "探索-blowfish-主题功能", "接下来会写什么", "欢迎来到我的新博客"]}}