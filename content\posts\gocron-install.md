---
title: "Gocron 实践安装，实现统一定时任务管理平台"
date: 2021-05-26T11:29:34+08:00
draft: false
tags: [ "gocron","docker"]
tags_weight: 60
categories: ["centos","linux"]
categories_weight: 30
keywords:
- gocron
- docker
- centos
- linux
- crontab
- 统一任务管理
description: "Gocron 实践安装，实现统一定时任务管理平台"
---



# 部署环境说明

- 操作系统: `CentOS release 7.8.2003 `
- Gocron Version: `v1.5.3`
- Mysql Version: `5.5`

# Gocron 说明

> `gocron` 是使用 `go` 语言开发的轻量级定时任务集中调度和管理系统, 可以用于替代 linux-crontab。
>
> [项目 github 地址](https://github.com/ouqiang/gocron)

# manage 端安装配置

> 在 Gocron 中数据的存储是放置在 `mysql` 数据库中的，我们这里需要配置一下 MySQl 的使用环境及权限。如果数据库已经事先安装，那么就只需要添加对应的库和用户使用权限即可。

## 手动使用二进制进行安装

- 下载对应版本安装包

  ```bash
  wget https://github.com/ouqiang/gocron/releases/download/v1.5.3/gocron-node-v1.5.3-linux-amd64.tar.gz # 下载对应压缩包
  
  # 解压 复制文件到指定文件夹下 进行使用
  tar xf gocron-v1.5.3-linux-amd64.tar.gz  
  cd gocron-linux-amd64/
  \cp -a gocron /usr/local/bin/
  
  gocron -v
  ```

- 配置为服务并设置自启动

  > 管理端启动后，服务默认监听端口是在 `tcp` 至上的 `5920` 端口
  
  ```bash
  mkdir -p /application/gocron-manage
  cat > /usr/lib/systemd/system/gocron-manage.service << EOF
  [Unit]
  Description=gocron web manage
  Documentation=https://github.com/ouqiang/gocron
  After=network.target
  
  [Service]
  Type=simple
  User=root
  ExecStart=/usr/local/bin/gocron web
  Restart=on-failure
  WorkingDirectory=/application/gocron-manage
  
  [Install]
  WantedBy=multi-user.target
  EOF
  ```
- 启动 manage 端并设置开机自启动

  ```bash
  systemctl start gocron-manage.service \
  && systemctl status gocron-manage.service \
  && systemctl enable gocron-manage.service
  ```

## 数据库的连接初始化

- 创建程序连接数据库的对应用户使用权限

  ```bash
  CREATE DATABASE `gocron` CHARACTER SET utf8mb4 COLLATE utf8mb4_bin; # 创建 程序连接数据库
  
  grant all on `gocron`.* to cron_app@'%' identified by 'xMPJ8Xkv1'; # 创建程序使用用户，并将对应库权限授予此用户进行使用
  
  flush privileges;
  ```

![image-20201026105133322.png](https://cdn.treesir.pub/images/2020/10/26/image-20201026105133322.png)

# agent/node 端安装配置

## 手动使用二进制进行安装

- 安装准备
  ```bash
  wget https://github.com/ouqiang/gocron/releases/download/v1.5.3/gocron-node-v1.5.3-linux-amd64.tar.gz # agent
  
  tar xf gocron-node-v1.5.3-linux-amd64.tar.gz \
  && cd gocron-node-linux-amd64 \
  && cp gocron-node /usr/local/bin  # 添加至环境变量中
  
  gocron-node --version # 测试效果
  ```

- 配置为服务并设置开机自启动

  ```bash
  mkdir -p /application/gocron-agent
  cat > /usr/lib/systemd/system/gocron-agent.service << EOF
  [Unit]
  Description=gocron agent/node server
  Documentation=https://github.com/ouqiang/gocron
  After=network.target
  
  [Service]
  Type=simple
  User=root
  ExecStart=/usr/local/bin/gocron-node -allow-root
  WorkingDirectory=/application/gocron-agent
  Restart=on-failure
  
  [Install]
  WantedBy=multi-user.target
  EOF
  
  # 设置开机自启动
  systemctl start gocron-agent.service \
  && systemctl status gocron-agent.service \
  && systemctl enable gocron-agent.service
  ```
  
  > 注意agent端 默认监听在 tcp 协议上的 5921 （--help 查看帮助文档 修改端口）

# 问题记录

## 日志提示主机名称过长

**具体日志表现如下所示:**

​	![image-20201026142218684](https://cdn.treesir.pub/images/2020/10/26/image-20201026142218684.png)

- 解决方法，就是更改对应 `表结构`，进行解决。

  ```bash
  ALTER TABLE cron_task_log MODIFY hostname text NOT NULL ;
  ```

  

# To Do
