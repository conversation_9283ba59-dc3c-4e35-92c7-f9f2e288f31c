---
title: "Argocd Cli Usage Tips"
date: 2023-07-24T14:19:17+08:00
draft: false
tags: [ "argocd"]
tags_weight: 80
categories: ["devops"]
categories_weight: 80
keywords:
- argocd
- cli
- 使用
- 一键化
description: "Argocd Cli 使用技巧"
---

# 说明

> 此篇文档，用于记录在使用  `ArgoCD CLi` 的过程中，所使用到的一些常用命令，`且供参考`。



---

# 使用记录

> ArgoCD cli [安装文档](https://argo-cd.readthedocs.io/en/stable/cli_installation/)，使用前，配置登录
>
> ```bash
>  argocd login xxx.argocd.xx
> ```

1. 一键 关闭 ArgoCD 下某个 Project 下所有 APP 的 同步

   ```bash
   for i in $(argocd app list -p cmb-custody-pet --grpc-web|awk '{print $1}' |grep -v 'NAME');do
       argocd app set "$i" --sync-option ApplyOutOfSyncOnly=false --grpc-web
       argocd app set "$i" --sync-policy none --grpc-web
   done
   ```

2. 显示 ArgoCD 中当前实例下所有 Project

   ```bash
   argocd proj list|awk '{print $1}'|grep -v 'NAME'
   ```

3. ArgoCD 初始化项目空间

    ```bash
    PROJ=repo-charts-dev
    argocd proj create "${PROJ}" --description 'repo dev 环境自动部署' \
    --dest https://kubernetes.default.svc,"${PROJ}" \
    --src 'https://gitlab-ee.treesir.pub/ci-cd/repo-charts.git'
    argocd proj allow-cluster-resource "${PROJ}" '*' ‘*'
    
    kubectl create ns "${PROJ}"
    ```

4. 一键 `销毁`· ArgoCD 下某个 Project 下所有 APP

   ```bash
   PROJ=repo-charts-dev
   for i in `argocd app list -p ${PROJ} --grpc-web|awk '{print $1}' |grep -v 'NAME'`;do
       argocd app delete "$i" --grpc-web -y
   done
   ```

5. ArgoCD 集群连接初始化，创建应用

   ```bash
   argocd login xxxx --grpc-web
   
   argocd cluster add  $(kubectl config get-contexts -o name) --grpc-web
   
   argocd app create guestbook \
   --repo https://github.com/argoproj/argocd-example-apps.git \
   --path guestbook \
   --dest-namespace default \
   --dest-server ${K8S_API_SERVER_ADDRESS} \
   --directory-recurse \
   --grpc-web
   ```

   

---

# ToDo

> 且供参考，后续补充。
