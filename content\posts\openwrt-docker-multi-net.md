---
title: "Docker 部署多网口 openWrt 软路由"
date: 2020-11-19T17:56:52+08:00 
draft: false
tags: [ "linux", "centos7"]
tags_weight: 20
categories: ["openwrt"]
categories_weight: 20
keywords:
- 软路由
- docker
- 多网口
- openwrt
- cetos
description: "Centos使用Docker部署多网口openwrt软路由进行科学上网"
---
## 环境说明: 

> 操作系统: Centos 7.8.2003 
>
> 内核版本: 4.14.129-bbrplus
>
> Docker 版本: 19.03.12
>
> 使用 Docker 镜像: [raymondwong/openwrt_r9:20.1.24-x86_64](https://hub.docker.com/r/raymondwong/openwrt_r9)
>
> 网卡说明: 板载螃蟹网卡、绿联 `usb3.0` 千M 网卡

## 系统网卡配置

### 将对应网卡启动混淆模式

  ```bash
  ip link set enp0s29u1u2 promisc on
  ip link set enp2s0 promisc on

  echo """ip link set enp0s29u1u2 promisc on
  ip link set enp2s0 promisc on""" >> /etc/rc.local    # 配置加入开机自启动

  chmod a+x /etc/rc.local
  ```

### Docker  创建虚拟网卡

> **主机规划:**
>
> - 我们现在要基于现有的网卡创建两个 `macvlan` 类型的网卡，这样后面创建的容器才能有网卡进行挂载使用，并且使用独立的网卡进行数据报文的通讯。`macnet1` 我们作为后面 `openWRT容器` 运行的 `Wan口` 使用, `macnet2` 则是 `Lan口`。
>
> - oepnWRT 使用网络说明  `(环境存在差异 按照你的环境配置参照修改即可)`
>   - wan: ***********/24
>   - lan：************/24 

  ```bash
docker network create -d macvlan --subnet=***********/24 --gateway=*********** -o parent=enp2s0 macnet1
  
docker network create -d macvlan -o parent=enp0s29u1u2 macnet2
  ```

![image-20201118173326706](https://cdn.treesir.pub/images/2020/11/18/image-20201118173326706.png)

## 容器网络配置

### 启动容器

> 这里启动时关联了创建的一张网卡，没有找到启动时关联多张网卡的方法，如有知道的小伙伴请留言告知。

```bash
docker run --name openwrt \
--restart always \
-d --network macnet2 \
--privileged raymondwong/openwrt_r9:20.1.24-x86_64 /sbin/init
```

### 添加第二块网卡

```bash
docker network connect macnet1 openwrt 
```

### 进入容器 修改网卡配置

```bash
docker exec -it openwrt bash
```

> 查看 发现没有 IP 我们需要手动配置一下

![image-20201119174448355](https://cdn.treesir.pub/images/2020/11/19/image-20201119174448355.png)

### 修改网卡配置文件： `/etc/config/network`

#### 默认的配置如下:

![image-20201119174511891](https://cdn.treesir.pub/images/2020/11/19/image-20201119174511891.png)

#### ***修改后的配置展示***

> ***Lan口配置了静态ip `***********11` ，`wan口` 通过使用 `dhcp` 的方式自动获取IP使用.***

```bash
cat > /etc/config/network << EOF
config interface 'loopback'
option ifname 'lo'
option proto 'static'
option ipaddr '127.0.0.1'
option netmask '*********'

config globals 'globals'
option ula_prefix 'fdc4:0ac4:85d4::/48'

config interface 'lan'
option ifname 'eth0'
option proto 'static'
option ipaddr '**************'
option netmask '*************'

config interface 'wan'
option ifname 'eth1'
option proto 'dhcp'

config interface 'vpn0'
option ifname 'tun0'
option proto 'none'
EOF
```

### 重启网卡

```bash
/etc/init.d/network restart
```

> ***再次检查一下 此时我们配置的 ip 已经有了***
>
> <img src="https://cdn.treesir.pub/images/2020/11/19/image-20201119172308290.png" alt="image-20201119172308290" />



## 客户端使用及优化

### 配置与 `Lan口` 为同一个网段的 ip

![image-20201119172657968](https://cdn.treesir.pub/images/2020/11/19/image-20201119172657968.png)

### 访问 `Dashboard`

> 我使用的这个容器默认的用户名密码为:
>
> - User: root
>
> - Passwold: password  

![image-20201119172802424](https://cdn.treesir.pub/images/2020/11/19/image-20201119172802424e954a00527e2cb9c.png)

![image-20201119172904817](https://cdn.treesir.pub/images/2020/11/19/image-20201119172904817b910116c02162b5d.png)



### 测试上网效果

#### `Ping 测试`

```bash
 ping -c 3 *********   # 发现无法 ping 通
```

- 检查路由

  - 常见操作系统查看路由表的方法
  - mac 通常使用命令：`ip route`
  - linux 通常使用命令:  `route -n`
  - windows 通常使用命令: `route print `

![image-20201119173121314](https://cdn.treesir.pub/images/2020/11/19/image-20201119173121314.png)

  检查路由发现是正确指向  `Lan口` 上面的网关的

### 导致客户端无法正常上网的解决方案

> :warning:  添加防火墙自定义规则进行解决

![image-*****************](https://cdn.treesir.pub/images/2020/11/19/image-*****************c99c37fa46048ce6.png)

  ![image-*****************](https://cdn.treesir.pub/images/2020/11/19/image-*****************05b2e8ef2319f700.png)

#### 将下面这个粘贴进去，注意修改示例中的网段为你 `Lan口上的网段` ,然后重启一下防火墙

```bash
iptables -t nat -I POSTROUTING -s ************/24  -j MASQUERADE
```

![image-*****************](https://cdn.treesir.pub/images/2020/11/19/image-*****************.png)

#### 再次测试一下 发现此时的网络已可以ping通 ~		
​				![image-*****************](https://cdn.treesir.pub/images/2020/11/19/image-*****************.png)
