---
title: "Centos7 执行 shutdown 无法正常关机的解决"
date: 2021-04-14T10:48:42+08:00
draft: false
tags: [ "centos7", "shell" ]
tags_weight: 42
categories: ["linux"]
categories_weight: 42
keywords:
- Linux
- Centos
- 无法正常
- 关机
- 执行
- 解决
- Centos7
description: "记录一次 centos7 无法正常关机的解决。"
---

# 环境说明

> 因为在公司中，有下班后有需要关闭 `服务器` 的操作，并且每天还需要去手动打开。有点违背 极客精神，所有自己通过 `shell script` + `cron` 的形式进行每天定时的 `开关服务器机` 操作。但是服务器中不乏有一些比较老的机型，没有提供类似于` ipmi` 管理功能，这些服务器自动启动依赖于主板自带的 `网络唤醒` 功能，如果服务器未正常关机时 (卡在关机界面)，第二天早上通过网络唤醒时是无法正常打开服务器的，需要管理人员的介入管理强制关机。这篇文档主要是介绍如何优化服务器的关机时间长，服务器关机卡死问题。

## 操作环境

- Centos7：`7.9.2009 `

# 优化



## 关闭图形界面

> 如服务器中，存在图形界面的功能，建议是把他设置成命令行形式的。

```bash
systemctl get-default # 查看当前的工作模式

systemctl set-default multi-user.target # 设置为命令行界面
```



## 设置 systemctl 的超时时间

```bash
cp /etc/systemd/system.conf{,.bak}

vi /etc/systemd/system.conf # 编辑此文件
DefaultTimeoutStopSec=90s  # 更改为 适合自己的时间
```

![image-20210414111258932](https://cdn.treesir.pub/img/image-20210414111258932.png)

## 更新系统

> 这里更新系统的原因是，可能是软件包中存在bug，导致关机时间长。

```bash
yum update -y \
&& yum upgrade -y
```



## 优化 **acpi** 管理模块

```bash
yum -y install acpid
systemctl start acpid
systemctl enable acpid

vim /etc/default/grub
GRUB_CMDLINE_LINUX="... acpi=force reboot=warm"  # 在此行最后添加
```

![image-20210414111742844](https://cdn.treesir.pub/img/image-20210414111742844.png)

```bash
grub2-mkconfig -o /boot/grub2/grub.cfg  # 配置后，重新生成 grub 启动加载文件
```



## 优化 watchdog 的超时时间

```bash
cat /etc/systemd/system.conf|grep -i ShutdownWatchdogSec  # 默认此参数为 10min
ShutdownWatchdogSec=30s
```



# 参考文档

> - [https://blog.csdn.net/F8qG7f9YD02Pe/article/details/108722168](https://blog.csdn.net/F8qG7f9YD02Pe/article/details/108722168)
> - [https://forums.centos.org/viewtopic.php?t=59735](https://forums.centos.org/viewtopic.php?t=59735)
