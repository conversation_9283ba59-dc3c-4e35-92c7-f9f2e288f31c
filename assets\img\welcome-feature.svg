<svg width="800" height="400" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="welcomeGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#74b9ff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#0984e3;stop-opacity:1" />
    </linearGradient>
    <filter id="shadow">
      <feDropShadow dx="2" dy="2" stdDeviation="3" flood-opacity="0.3"/>
    </filter>
  </defs>
  
  <rect width="800" height="400" fill="url(#welcomeGrad)"/>
  
  <!-- Welcome text background -->
  <rect x="50" y="150" width="700" height="100" rx="20" fill="#ffffff" opacity="0.9" filter="url(#shadow)"/>
  
  <!-- Decorative elements -->
  <circle cx="150" cy="80" r="30" fill="#ffffff" opacity="0.3"/>
  <circle cx="650" cy="320" r="40" fill="#ffffff" opacity="0.2"/>
  <polygon points="700,50 720,30 740,50 720,70" fill="#ffffff" opacity="0.4"/>
  
  <!-- Text -->
  <text x="400" y="190" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#2d3436">Welcome!</text>
  <text x="400" y="220" text-anchor="middle" font-family="Arial, sans-serif" font-size="18" fill="#636e72">My Blowfish Blog</text>
</svg>
